/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS and SCSS file within this directory, lib/assets/stylesheets, or any plugin's
 * vendor/assets/stylesheets directory can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the bottom of the
 * compiled file so the styles you add here take precedence over styles defined in any other CSS/SCSS
 * files in this directory. Styles in this file should be added after the last require_* statement.
 * It is generally better to create a new file per style scope.
 *
 *= require_tree .
 *= require_self
 */

 body, p, h1, h2, h3, h4, h5, h6 {
  font-family: 'Noto Sans TC', 'Microsoft JhengHei', Arial, Helvetica, sans-serif;
 }

 .notice, .alert {
  font-size: 1.4em;
 }

 .notice {
  color: #09af00;
 }

 .alert {
  color: #F50057;
 }

 .opaque {
  opacity: 0.4;
 }

 .app-dashboard ul.menu li a, .app-dashboard-close-sidebar a {
  font-size: 1.2em;
  color: #3d3779;
 }

 .app-dashboard div.status_change_block {
  border: 2px solid #1779ba;
  padding: 2em;
  margin-bottom: 5em;
 }

 .app-dashboard input[type=submit] {
  background: #1565C0;
  padding: 1em 1.5em;
  color: white;
  font-weight: bold;
  border-radius: 90px;
 }

 .app-dashboard ul.menu li a:hover {
  background-color: #08456d;
  color: white;
 }

 .app-dashboard table td {
  font-size: 1.2em;
  border: 1px solid #efefef;
 }

 .app-dashboard div.input.date select {
  width: auto;
 }

 .app-dashboard form.search_bar{
  width: 100%;
  display: flex;
  align-items: center;
 }


  .dashboard h2{
    border-bottom: 1px solid #cecece;
  }

  .dashboard .row {
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 3em;
  }

  .process_form hr {
    margin-top: 3em;
    margin-bottom: 3em;
    border-bottom: 1px dashed lightsteelblue;
    border-top: 1px dashed lightsteelblue;
  }

  .process_form .signature {
    font-family: cursive;
    font-weight:  bold;
    color: steelblue;
  }

  .process_form .disapproved {
    color:  red;
    font-weight: bold;
  }

  .process_form .approval-button {
    display: inline-block;
    background: yellow;
    padding: .5em 1em;
    border-radius:  3px;
    border-top:  1px solid yellow;
    border-bottom:  1px solid orange;
    color:  black;
    text-decoration: none;
  }
  .process_form .approval-button:hover {
    background:  orange;
    border-bottom-color: darkorange ;
  }

  .process_form .disapproval-button {
    display: inline-block;
    background: red !important;
    padding: .5em 1em;
    border-radius:  3px;
    border-top:  1px solid yellow;
    border-bottom:  1px solid orange;
    color:  black;
    text-decoration: none;
  }
  .process_form .disapproval-button:hover {
    background:  darkorange !important;
    border-bottom-color: red ;
  }

  main.project {
    position: relative;
    padding: 1em;
    border: 1px solid #eee;
    box-shadow: 0 1px 5px -3px rgba(0, 0, 0, 0.5);
    border-radius: 0.2em;
  }

  main.project > header {
    position: sticky;
    top: 0;
    background: white;
    box-shadow: 0 3px 5px -4px rgba(0, 0, 0, 0.6);
    margin-bottom: 1em;
  }

  main.project > .top-right-memo {
    background: #ffff86;
    text-align: center;
    position: absolute;
    top: -2rem;
    right: -4rem;
    line-height: 1.5em;
    font-size: 1.2rem;
    width: 220px;
    padding: 2em;
    box-shadow: 0 1px 5px -3px rgba(0, 0, 0, 0.5);
    transform: rotate(5deg);
    z-index: 9;
  }

  main.project p {
    padding-bottom: .5em;
  }

  .fast-check-block {
    border-top: 1px solid #808080;
    border-bottom: 1px solid #808080;
    padding-bottom: 1em;
    margin-top: 3em;
  }

  .fast-check-block .operation {
    text-align: right;
    padding: 1.2em;
    font-size: 2em;
  }

  .fast-check-block .container {
    display: flex;
    flex-grow: 3;
    gap: 1.5em;
  }

  .fast-check-block .container > div {
    flex: 1;
  }

  .apply-filter {
    margin: 3em auto;
  }

  .apply-filter a {
    padding: .7em;
    border: 1px solid #1779ba;
    color: #1779ba;
  }

  .apply-filter a:hover {
    border: 1px solid #353535;
    color: #353535;
  }

  .function-area a {
    font-size: 1em;
  }

  .function-area .function-tabs {
    padding-right: 0em;
  }

  .function-area .function-blocks {
    padding-left: 0em;
    border-left: 1px solid #e6e6e6;
  }

  .overview-block {
    height: 185px;
    font-size: 1.2em;
  }

  .overview-block a {
    display: block;
    height: 100%;
    border: 1px solid #999;
    border-radius: 10px;
  }

  .overview-block.give_up a {
    color: #e94444;
    border: 1px solid #e94444;
  }

  .overview-block a:hover {
    text-decoration: underline;
    color: #f57f17;
    border: 1px solid #f57f17;
  }

  .overview-block a .center_vertically {
    position: relative;
    top: 50%;
    transform: translateY(-50%);
  }

  a.label {
    display: flex;
    background-color: white;
    border: 1px solid #1779ba;
    color: #1779ba;
    align-items: center;
    padding: 0;
    margin: 1em 6em 1em 0;
  }

  a.label:hover {
    cursor: pointer;
    background-color: #1779ba;
    color: white;
  }

  a.label > * {
padding: 1em;
    font-size: 1.5em;
      }

  a.label div.name {
    overflow: hidden;
    flex: 8;
  }

  a.label div.number {
    flex: 1;
    border-left: 1px solid #1779ba;
    text-align: center;
  }

  a.label.drawing {
    border: 1px solid #3adb76;
    color: #3adb76;
  }

  a.label.drawing:hover {
    background-color: #3adb76;
    color: white;
  }

  a.label.drawing div.number {
    border-left: 1px solid #3adb76;
  }

  a.label.training {
    border: 1px solid #f57f17;
    color: #f57f17;
  }

  a.label.training:hover {
    background-color: #f57f17;
    color: white;
  }

  a.label.training div.number {
    border-left: 1px solid #f57f17;
  }

  a.label.supplies {
    border: 1px solid #5D4037;
    color: #5D4037;
  }

  a.label.supplies:hover {
    background-color: #5D4037;
    color: white;
  }

  a.label.supplies div.number {
    border-left: 1px solid #5D4037;
  }

  ul.attachment-list {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
  }

  ul.attachment-list li {
    margin: 0 1em;
    text-align: center;
  }

 .app-dashboard {
  height: 100vh;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}

.app-dashboard-body {
  -webkit-flex: 1 1 auto;
      -ms-flex: 1 1 auto;
          flex: 1 1 auto;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.app-dashboard-top-nav-bar {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-justify-content: flex-start;
      -ms-flex-pack: flex-start;
          justify-content: flex-start;
  background: #2c3840;
  height: 55px;
  width: 100%;
  -webkit-flex: 0 0 55px;
      -ms-flex: 0 0 55px;
          flex: 0 0 55px;
}

.app-dashboard-top-nav-bar .menu-icon {
  vertical-align: text-bottom;
}

.app-dashboard-logo {
  color: #fefefe;
  text-transform: uppercase;
  font-weight: bold;
}

.app-dashboard-search-bar-container {
  position: relative;
}

.app-dashboard-search {
  background: #e0e0e0;
  border: 0;
  margin-bottom: 0 !important;
  color: #202020;
}

.app-dashboard-search-icon {
  position: absolute;
  color: #fefefe;
  right: 1rem;
  top: 50%;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
          transform: translateY(-50%);
}

.app-dashboard-top-bar-actions {
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.app-dashboard-top-bar-actions .login-name {
  color: #fefefe;
}

.app-dashboard-top-bar-actions button {
  margin-bottom: 0;
  margin-right: 2rem;
}

.app-dashboard-top-bar-actions button.hollow {
  border-color: #fefefe;
  color: #fefefe;
}

.app-dashboard-top-bar-actions button.hollow:hover {
  background: #fefefe;
  color: #1779ba;
}

.app-dashboard-top-bar-actions .fa-info-circle {
  color: #fefefe;
  font-size: 1.5rem;
}

.app-dashboard-top-bar-actions .button {
  margin-bottom: 0;
}

.app-dashboard-sidebar {
  background-color: #fefefe;
  height: 100%;
  overflow-x: visible;
  overflow-y: auto;
  z-index: 1;
  transition: all 0.5s ease;
}

.apply.border {
  border: 2px solid #cecece;
}

.app-dashboard-sidebar .app-dashboard-open-sidebar, .app-dashboard-sidebar .app-dashboard-close-sidebar {
  -webkit-align-items: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 2rem 1rem;
}

.app-dashboard-sidebar .app-dashboard-sidebar-close-button {
  font-size: 14px;
}

.app-dashboard-sidebar .app-dashboard-sidebar-inner {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

.app-dashboard-sidebar .app-dashboard-sidebar-inner .menu > li > a {
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.app-dashboard-sidebar .fa.large {
  font-size: 1.5rem;
  width: 40px;
}

.reveal-for-medium .app-dashboard-open-sidebar {
  display: none;
}

.app-dashboard-sidebar-footer {
  background: rgba(42, 57, 79, 0.8);
  bottom: 0;
  left: 0;
  padding: 1rem;
  position: absolute;
  width: 100%;
}

.app-dashboard-open-sidebar {
  text-align: center;
}

.app-dashboard-body-content {
  transition: all 0.5s ease;
  overflow-y: auto;
  -webkit-flex: 1 1 0;
      -ms-flex: 1 1 0px;
          flex: 1 1 0;
  padding: 50px;
  padding-top: 0;
  background-color: #fefefe;
}

.app-dashboard-body-content .main-content {
  padding-top: 1.5em;
  min-height: 45rem;
}

.app-dashboard-body-content .main-content .pagination a{
  display: inline-block;
}

.app-dashboard-body-content .main-content .pagination span.active {
  text-decoration: underline;
}

.app-dashboard-body-content .main-content table.review {
  margin-bottom: 3em;
}

.app-dashboard-body-content .main-content table.review tr:nth-child(even) td {
  background-color: #f5f5f5;
}

.app-dashboard-body-content .main-content table.review th {
  font-size: 1.2em;
  background-color: lightgray;
}

ul.attachment-list span{
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.app-dashboard nav.main {
  text-align: center;
}

.app-dashboard nav.main a {
  color: #fefefe;
  padding: 0 1.2em;
}

.app-dashboard nav.main a:hover {
  text-decoration: underline;
}


.shrink-large .off-canvas-content {
  margin-left: 90px !important;
}

@media screen and (min-width: 40em) and (max-width: 63.9375em) {
  .app-dashboard.shrink-medium .app-dashboard-close-sidebar, .app-dashboard.shrink-medium .app-dashboard-sidebar-text {
    display: none;
  }
  .app-dashboard.shrink-medium .app-dashboard-open-sidebar {
    display: block;
  }
  .app-dashboard.shrink-medium .app-dashboard-sidebar {
    width: 80px;
  }
  .app-dashboard.shrink-medium .app-dashboard-sidebar .fa.large {
    width: auto;
  }
  .app-dashboard.shrink-medium .off-canvas-content {
    margin-left: 80px;
    width: calc(100% - 80px);
  }
  .app-dashboard.shrink-medium .navigation {
    margin-top: 2rem;
    text-align: center;
  }
  .app-dashboard.shrink-medium .menu.vertical > li > a {
    -webkit-justify-content: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .app-dashboard.shrink-medium .menu li::after {
    display: none;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
  }
  .app-dashboard.shrink-medium .menu li a {
    padding: 0.75rem;
  }
  .app-dashboard.shrink-medium .menu li a svg {
    margin: 0;
  }
  .app-dashboard.shrink-medium .menu li a span {
    display: none;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
  }
}

@media print, screen and (min-width: 64em) {
  .app-dashboard.shrink-large .app-dashboard-close-sidebar, .app-dashboard.shrink-large .app-dashboard-sidebar-text {
    display: none;
  }
  .app-dashboard.shrink-large .app-dashboard-open-sidebar {
    display: block;
  }
  .app-dashboard.shrink-large .app-dashboard-sidebar {
    width: 80px;
  }
  .app-dashboard.shrink-large .app-dashboard-sidebar .fa.large {
    width: auto;
  }
  .app-dashboard.shrink-large .off-canvas-content {
    margin-left: 80px;
    width: calc(100% - 80px);
  }
  .app-dashboard.shrink-large .navigation {
    margin-top: 2rem;
    text-align: center;
  }
  .app-dashboard.shrink-large .menu.vertical > li > a {
    -webkit-justify-content: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .app-dashboard.shrink-large .menu li::after {
    display: none;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
  }
  .app-dashboard.shrink-large .menu li a {
    padding: 0.75rem;
  }
  .app-dashboard.shrink-large .menu li a svg {
    margin: 0;
  }
  .app-dashboard.shrink-large .menu li a span {
    display: none;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
  }
}
