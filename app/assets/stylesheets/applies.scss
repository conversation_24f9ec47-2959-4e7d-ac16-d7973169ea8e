// Place all the styles related to the Apply controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: https://sass-lang.com/
$must-color: #ee3238;

html, body {
  height: 100%;
}

#apply {
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  div.main {
    padding-top: 0;
  }

  h1, h2 {
   text-align: center;
  }

  h2 {
   margin: 0.3em 0;
  }

  p {
   font-size: 1.2em;
  }

  p.must {
    color: #ee3238;
  }

  label {
   font-size: 1em;
  }

  header {
   padding: 20px 0;

   img {
    margin: 10px;
   }
  }

  div.must {
    color: $must-color;
    margin: 1em 0;
  }

  p.must {
    color: $must-color;
    font-size: 1.5em;
   }

   ul.must {
     li {
       color: $must-color;
       font-size: 1.5em;
       line-height: 1.5em;
     }
   }

   .must-field {
    &:before {
     content: "* ";
     color: $must-color;
    }
   }

  .main-spliter {
    height: 2px;
    border: 0;
    background: linear-gradient(to left, #fff, #f7270b, #fff);
  }

  .page {
  border: 1px solid #888;
  padding: 2rem;
  min-height: 600px;
  width: 80%;
  margin: 0 auto;
  background-color: #f0f0f1;
  box-shadow: rgba(0, 0, 0, 0.15) 0px 15px 25px, rgba(0, 0, 0, 0.05) 0px 5px 10px;

  p, li {
    font-size: 1.3em;
    line-height: 1.3em;
  }

  .content {
    min-height: 450px;
  }
 }

 footer {
  background-color: #e5e7eb;

  p {
    font-size: 1em;
  }
 }

}

#apply > * {
  padding: 2rem;
}

#loadingModal h2 {
  margin: 0.5em;
}

.lds-ring {
  display: block;
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
}
.lds-ring div {
  box-sizing: border-box;
  display: block;
  position: absolute;
  width: 64px;
  height: 64px;
  margin: 8px;
  border: 8px solid #999;
  border-radius: 50%;
  animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  border-color: #999 transparent transparent transparent;
}
.lds-ring div:nth-child(1) {
  animation-delay: -0.45s;
}
.lds-ring div:nth-child(2) {
  animation-delay: -0.3s;
}
.lds-ring div:nth-child(3) {
  animation-delay: -0.15s;
}
@keyframes lds-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}


@media screen and (max-width: 768px) {
  #apply {
    .page {
      width: 100%;
    }
  }
}