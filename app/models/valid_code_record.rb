class ValidCodeRecord < ApplicationRecord

  def self.increase apply_id
    record = self.find_by apply_id: apply_id, date: Date.today
    if record
      record.update(try_count: record.try_count + 1)
    else
      self.create apply_id: apply_id, date: Date.today, try_count: 1
    end
  end

  def self.overlimit? apply_id, limit = 10
    record = self.find_by apply_id: apply_id, date: Date.today
    !record or record.try_count > limit
  end

  def self.resetlimit apply_id
    record = self.find_by apply_id: apply_id, date: Date.today
    if record
      record.update(try_count: 0)
    end
  end
end