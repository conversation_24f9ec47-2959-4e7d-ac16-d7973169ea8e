class CateringM1List
  M1_NUMBERS = ["123316", "204492", "62578", "251352", "118733", "240865", "117675", "118442", "215085", "250507", "227820", "199977", "232834", "152272", "100684", "103450", "098968", "214040", "265546", "206754", "247203", "149881", "33215", "218251", "240305", "173404", "72137", "254795", "283034", "207625", "193419", "20568", "183353", "78434", "203630", "260094", "200663", "280166", "67490", "177551", "188626", "188849", "249579", "134642", "108426", "255974", "214505", "205146", "271920", "90839", "168724", "228764", "224165", "80962", "256622", "222313", "267754", "221698", "278560", "58109", "204761", "133623", "257028", "267467", "232641", "280650", "16525", "236929", "191392", "157382", "127286", "251279", "92928", "273541", "15419", "270125", "232270", "189663", "129282", "087351", "83828", "228117", "175453", "266787", "46785", "228281", "156593", "106839", "283503", "284665", "219771", "81218", "108487", "186382", "210875", "214238", "200829", "247951", "274776", "142439", "86902", "175754", "042558", "249310", "85013", "052622", "293692", "251210", "168588", "289417", "112098", "177474", "268371", "283692", "025065", "137977", "258924", "094097", "284175", "300058", "283256", "115687", "165143", "151044", "213030", "282201", "239109", "243380", "263037", "94215", "214813", "223517", "198445", "287107", "206598", "108140", "174174", "257872", "239144", "214008", "199521", "135844", "93185", "295060", "223557", "291635", "240227", "149849", "38542", "207185", "147413", "023306", "207187", "98367", "218945", "201278", "159909", "091557", "195492", "210887", "42829", "95117", "189167", "283211", "285769", "092159", "184796", "87845", "113090", "290049", "198803", "211671", "205296", "223437", "051225", "64459", "280356", "243660", "221625", "179780", "101494", "285642", "222313", "265672", "256622", "249579", "209112", "275646", "294408", "087122", "270135", "024016", "267754", "96856", "291931", "260055", "122851", "83041", "196075", "287945", "111158", "169178", "298260", "280597", "269996", "287389", "213959", "197692", "267174", "228856", "281213", "222793", "298631", "181957", "193247", "250229", "230201", "189292", "208849", "292560", "176641", "213882", "099635", "217848", "167665", "109237", "301806", "298735", "18347", "137761", "240426", "269455", "196583", "193937", "285340", "253418", "251669", "295474", "220263", "298618", "214276", "243402", "195104", "84232", "237683", "220506", "193135", "293891", "227416", "196347", "211283", "52693", "226875", "289355", "64963", "248953", "253922", "114141", "159060", "219669", "218788", "190307", "206764", "161939", "112548", "258167", "159656", "213976", "084872", "134701", "200740", "107294", "102613", "244498", "113089", "113934", "102372", "94908", "285381", "288133", "247092", "210006", "126005", "137150", "280298", "273084", "242113", "212090", "281799", "202948", "275424", "185513", "83339", "52776", "113165", "176827", "34876", "110552", "212479", "203497", "212690", "229777", "247798", "270323", "203380", "294597", "127843", "93992", "14040", "211935", "229973", "249102", "109278", "012642", "48295", "125919", "302197", "202463", "183512", "30167", "298655", "232015", "252798", "080381", "168581", "85802", "264907", "295530", "199005", "76931", "108399", "191130", "297521", "178676", "191030", "72542", "285301", "185636", "291358", "76375", "113388", "58539", "221698", "16525", "184167", "280884", "176712", "293536", "150894", "161430", "149450", "215132", "246135", "176390", "214222", "222267", "296436", "260671", "111587", "239925", "127880", "189624", "270037", "103138", "274381", "302329", "052664", "211886", "205309", "48011", "226258", "110226", "250148", "218747", "86064", "210873", "293407", "213802", "302214", "109182", "258994", "191921", "50101", "264539", "189185", "089151", "117997", "252283", "292340", "294617", "275566", "256832", "278857", "125294", "210098", "211873", "57524", "288934", "175918", "265331", "024520", "171791", "209909", "297723", "231387", "182534", "250716", "248868", "208399", "214597", "141437", "296333", "196205", "145674", "248264", "195048", "209246", "16166", "219027", "185934", "214275", "233045", "206729", "197441", "209692", "156704", "128111", "196533", "290835", "243288", "302715", "97569", "186960", "198476", "115568", "199589", "243547", "288187", "236597", "214347", "241304", "205861", "220642", "244579", "190414", "211563", "177114", "207400", "169316", "302224", "221197", "154864", "92604", "302384", "259224", "194956", "282834", "301089", "215888", "232542", "292867", "64764", "300896", "271077", "294713", "247999", "170148", "302373", "22201", "220523", "282646", "215802", "292799", "299599", "215558", "299688", "282032", "156129", "110415", "266139", "218010", "142912", "281694", "257434", "220643", "237744", "144949", "151481", "283758", "219653", "209495", "227207", "99043", "182989"].freeze

  def self.include?(number)
    M1_NUMBERS.include?(number)
  end
end