class Assessment < ApplicationRecord

  scope :by_scheme, ->(scheme_id) { joins(:apply).where(apply: { scheme_id: scheme_id }) }
  scope :open_approval_scheme, -> { joins(:apply).where(apply: { scheme_id: Scheme.where(is_open_for_approval: true).ids }) }
  scope :yet_finished_training, -> { where(is_finished_training: false) }
  scope :finished_training, -> { where(is_finished_training: true) }
  scope :absent_training, -> { where(is_absent_training: true) }
  scope :yet_finished_report, -> { where(is_open_data_to_supplies: false) }
  scope :finished_report, -> { where(is_open_data_to_supplies: true) }

  belongs_to :apply, -> { unscope(where: :scheme_id) }

  has_many :user_assessments
  has_many :users, through: :user_assessments

  has_many_attached :report_scan_attachments
  has_many_attached :checking_photos_attachments

  has_rich_text :assessment_remark
  has_rich_text :training_remark
end