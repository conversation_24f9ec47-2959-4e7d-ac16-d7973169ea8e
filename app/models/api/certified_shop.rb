class Api::CertifiedShop

  def self.getStatus companier_tax_number
    url = "https://www.consumer.gov.mo/csenq/statusenquiry/result"
    form_data = {
      file_no: companier_tax_number
    }

    begin
      response = RestClient.post(
        url,
        form_data
      )

      # 解析 JSON 回應
      @response_data = JSON.parse(response.body)
      msg = @response_data["msg"]    # 提取 msg 字段

    rescue RestClient::ExceptionWithResponse => e
      msg = "API 請求失敗: #{e.response.code}"
    end

    msg
  end
end