class Stage

  STAGES = [
  "資料確認",
  "已抽籤",
  "培訓及診斷",
  "供應商匹配及提交",
  "待發出合意安裝通知書",
  "審批",
  "系統安裝及供應商上傳結算文件",
  "結算審批",
  "完成"].freeze

  def self.getFirstStageName
    STAGES[0]
  end

  # 初始化時設定當前階段
  def initialize(current_stage)
    @current_stage = current_stage
  end

  def getPath
    return [] unless STAGES.include?(@current_stage)

    current_index = STAGES.index(@current_stage)
    STAGES[0..current_index]
  end

  # 進入下一階段
  def next
    return nil if completed? # 如果已經完成，返回 nil

    STAGES[STAGES.index(@current_stage) + 1]
  end

  # 檢查是否已完成（最後階段）
  def completed?
    @current_stage == STAGES.last
  end

  # 檢查某階段是否正是當前階段
  def equal?(stage)
    return false unless STAGES.include?(@current_stage) && STAGES.include?(stage)

    STAGES.index(stage) == STAGES.index(@current_stage)
  end

  # 檢查某階段是否在當前階段之後
  def ahead?(stage)
    return false unless STAGES.include?(@current_stage) && STAGES.include?(stage)

    STAGES.index(stage) > STAGES.index(@current_stage)
  end

  # 檢查某階段是否在當前階段之前
  def behind?(stage)
    return false unless STAGES.include?(@current_stage) && STAGES.include?(stage)

    STAGES.index(stage) < STAGES.index(@current_stage)
  end


end