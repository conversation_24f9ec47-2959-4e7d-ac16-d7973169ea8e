class SettlementApproval < ApplicationRecord

  scope :by_scheme, ->(scheme_id) { joins(:apply).where(apply: { scheme_id: scheme_id }) }
  scope :problems, -> { where.not(comment_by_manager: [nil, '']) }
  scope :for_manager_approval, -> { where(approved_by_manager: nil) }
  scope :for_sm_approval, -> { where.not(approved_by_manager: nil).where(approved_by_sm: nil) }
  scope :for_fin_verify, -> { where.not(approved_by_sm: nil).where(verified_by_fin: nil) }
  scope :for_ddg_approval, -> { where.not(verified_by_fin: nil).where(approved_by_ddg: nil) }
  scope :for_dg_approval, -> { where.not(approved_by_ddg: nil).where(approved_by_dg: nil) }

  scope :finished, -> (is_finished) {
    joins(apply: :suppliesApply).where(suppliesApply: { is_finish_settlement: is_finished })
  }

  belongs_to :apply, -> { unscope(where: :scheme_id) }

  def self.getWaitingApprovalsBaseOnUser user
    if user.is_thomas?
      return for_manager_approval
    elsif user.is_sm?
      return for_sm_approval
    elsif user.is_fin?
      return for_fin_verify
    elsif user.is_ddg?
      return for_ddg_approval
    elsif user.is_dg?
      return for_dg_approval
    end
    where(id: [nil])  #empty approvals list
  end
end