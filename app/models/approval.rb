class Approval < ApplicationRecord

  scope :by_scheme, ->(scheme_id) { joins(:apply).where(apply: { scheme_id: scheme_id }) }
  scope :problems, -> { where.not(comment_by_manager: [nil, '']) }
  scope :for_manager_approval, -> { where(approved_by_manager: nil) }
  scope :for_sm_approval, -> { where.not(approved_by_manager: nil).where(approved_by_sm: nil) }
  scope :for_ddg_approval, -> { where.not(approved_by_sm: nil).where(approved_by_ddg: nil) }
  scope :for_dg_approval, -> { where.not(approved_by_ddg: nil).where(approved_by_dg: nil) }

  belongs_to :apply, -> { unscope(where: :scheme_id) }
end