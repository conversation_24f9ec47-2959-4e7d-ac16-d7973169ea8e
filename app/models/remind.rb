class Remind < ApplicationRecord
  belongs_to :apply

  def self.sort_by_days_since_last_step
    all.sort_by { |record| record.get_days_since_last_step }
  end

  def self.steps_and_expired_days
    {
      finished_training: 0,
      to_assessment: 10,
      submit_assessment_report: 7,
      submit_quotation: 14,
      submit_agreement_report: 7,
      submit_settlement: 45,
      to_audit: 12,
      submit_epq: 12,
      case_finished: 90,
      all_finished: 0
    }
  end

  def get_next_step
    next_step_index = Remind.steps_and_expired_days.keys.index(self.current_step.to_sym) + 1
    Remind.steps_and_expired_days.keys[next_step_index]
  end

  def update_to_next_step
    update_attribute(:current_step, self.get_next_step) if get_next_step != nil
  end

  def get_current_step_expired_days
    Remind.steps_and_expired_days[self.current_step.to_sym]
  end

  def get_days_since_last_step()
    self.get_current_step_expired_days - (Date.today - self.last_step_finished_at).to_i
  end

  def get_steps_options
    options = []
    Remind.steps_and_expired_days.keys.each_with_index do |step, index|
      break if index == Remind.steps_and_expired_days.size - 1

      if step == self.current_step.to_sym
        options << [I18n.t(step.to_s()), step, selected: true]
      elsif index < Remind.steps_and_expired_days.keys.index(self.current_step.to_sym)
        options << [" ✔ #{I18n.t(step)} ", step]
      else
        options << [" (未開始) #{I18n.t(step)} ", step, disabled: true]
      end
    end
    options
  end

  def get_steps_checkboxes
    options = []
    Remind.steps_and_expired_days.keys.each_with_index do |step, index|
      break if index == Remind.steps_and_expired_days.size - 1

      if step == self.current_step.to_sym
        options << [I18n.t(step.to_s()), "is_go_next_step", required: "required" ]
      elsif index < Remind.steps_and_expired_days.keys.index(self.current_step.to_sym)
        options << ["#{I18n.t(step)} ", "pass_step", checked: true]
      else
        options << [" (未開始) #{I18n.t(step)} ", "future_step", disabled: true]
      end
    end
    options
  end


end
