class Scheme < ApplicationRecord

  MAX_APPLIES_COUNT = 3

  validates :name, presence: true
  validates :case_number_prefix, presence: true

  has_many :applies
  has_many :supplies_apply

  def self.current
    self.last
  end

  

  def to_s
    name
  end

  def in_apply_duration?
    current_time = DateTime.now
    self.enrollment_start_at.beginning_of_day <= current_time && current_time <= self.enrollment_end_at.end_of_day && self.is_allow_enrollment
  end

end
