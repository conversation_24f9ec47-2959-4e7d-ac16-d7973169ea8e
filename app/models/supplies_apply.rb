class SuppliesApply < ApplicationRecord

  belongs_to :user
  belongs_to :apply, optional: true
  belongs_to :scheme

  before_save :trim_values

  scope :by_scheme, ->(scheme_id) { where(scheme_id: scheme_id) }
  scope :open_approval_scheme, -> { where(scheme_id: Scheme.where(is_open_for_approval: true).ids) }
  scope :for_user, ->(user_id) { where(user_id: user_id) }
  scope :with_settlement, -> { joins(:settlement_receipt_attachments_attachments).distinct }
  scope :finished_settlement,  -> { where(is_finish_settlement: true) }

  has_many_attached :quotation_attachments
  has_many_attached :authorization_letter_attachments
  has_many_attached :install_agreement_letter_attachments

  has_many_attached :settlement_receipt_attachments
  has_many_attached :settlement_installment_attachments
  has_many_attached :settlement_training_attachments
  has_many_attached :settlement_online_pay_attachments
  has_many_attached :settlement_others_attachments

  has_many_attached :install_report_code_photos

  has_many_attached :settlement_checking_photos  #結算前稽查照片

  validates :companies_name, presence: { message: "商戶名稱必須填" }
  validates :companies_tax_number, presence: { message: "商戶場所登記編號必須填" }
  validates :apply_code, presence: { message: "商戶申請編號必須填" }

  MAX_ATTACHMENT_SIZE = 10.megabyte

  validates :quotation_attachments, attached: { message: '請上傳報價單文件' }, size: { less_than: MAX_ATTACHMENT_SIZE, message: '上傳報價單文件過大，限制為 10MB 以內' }
  validates :authorization_letter_attachments, attached: { message: '請上傳商戶授權書文件' }, size: { less_than: MAX_ATTACHMENT_SIZE, message: '上傳商戶授權書文件過大，限制為 10MB 以內' }
  validates :settlement_receipt_attachments, size: { less_than: MAX_ATTACHMENT_SIZE, message: '上傳安裝系統收據文件過大，限制為 10MB 以內' }
  validates :settlement_installment_attachments, size: { less_than: MAX_ATTACHMENT_SIZE, message: '上傳安裝系統後的相片／圖片過大，限制為 10MB 以內' }
  validates :settlement_training_attachments, size: { less_than: MAX_ATTACHMENT_SIZE, message: '上傳培訓記錄文件過大，限制為 10MB 以內' }
  validates :settlement_online_pay_attachments, size: { less_than: MAX_ATTACHMENT_SIZE, message: '上傳申請線上支付的相關文件證明文件過大，限制為 10MB 以內' }
  validates :settlement_others_attachments, size: { less_than: MAX_ATTACHMENT_SIZE, message: '上傳其他補充資料文件過大，限制為 10MB 以內' }
  validates :install_report_serial_number, allow_blank: true, format: { with: /\A[A-Z0-9-]+\z/, message: '報告編號只可包含數字、大寫字母和 - 符號' }
  validates :install_report_code_photos, size: { less_than: MAX_ATTACHMENT_SIZE, message: '上傳資料文件過大，限制為 10MB 以內' }
  validates :settlement_checking_photos, size: { less_than: MAX_ATTACHMENT_SIZE, message: '上傳資料文件過大，限制為 10MB 以內' }

  def self.search query
    query = query.strip
    distinct.where("apply_code like :query or companies_name like :query", query: "%#{query}%")
  end

  def trim_values
    attributes.each do |attr_name, value|
      self[attr_name] = value.strip if value.is_a?(String)
    end
  end

  def barcode_for_report
    return if install_report_serial_number.blank?
    require 'barby'
    require 'barby/barcode/code_39'
    require 'barby/outputter/html_outputter'
    barcode = Barby::Code39.new install_report_serial_number.force_encoding('ASCII-8BIT')
    barcode.to_html
  end

  def matched_apply?
    self.apply_id.present?
  end


  def dg_approvals?
    self.apply.present? && self.apply.approval.present? && self.apply.approval.approved_by_dg
  end

  def to_s
    self.companies_name
  end

end
