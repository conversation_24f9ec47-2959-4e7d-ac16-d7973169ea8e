class Apply < ApplicationRecord

  attr_accessor :is_owner_tax_number_registered

  def initialize(attributes = {})
    super
    @is_owner_tax_number_registered = false
  end

  INDUSTRY_LIST = ["農業，畜牧業，狩獵及林業", "捕魚業", "採礦工業", "製造業", "電力、氣體及水的生產及分", "建築", "批發及零售業；機動車、摩托車、個人及家庭物品的維修", "住宿、餐廳、酒樓及同類場所", "運輸、貯藏及通訊", "金融業務", "不動產業務、租賃及向企業提供的服務", "教育", "醫療衛生及社會福利", "團體、社會及個人的其他服務", "國際組織及其他領土以外的機構"].freeze

  # 0 - audit_waiting - 等待審閱中
  # 1 - audit_approved  - 審閱通過
  # 2 - audit_question - 審閱存疑問
  enum current_audit_status: { audit_waiting: 'audit_waiting', audit_approved: 'audit_approved', audit_question: 'audit_question' }


  default_scope { where(is_soft_deleted: false).or(where(is_soft_deleted: nil)) }

  scope :by_scheme, ->(scheme_id) { where(scheme_id: scheme_id) }
  scope :by_stage, ->(stage) { where(current_stage: stage) }
  scope :registered, -> { where(current_confirm_status: 'registered') }
  scope :passed, -> { where(current_confirm_status: 'passed') }
  scope :no_passed, -> { where(current_confirm_status: 'no_passed') }
  scope :need_resubmit, -> { where(is_need_resubmit_attachments: true) }
  scope :managment_waiting_approved, -> { audit_waiting.data_confirmed.where(confirmations: { is_not_reveal_in_managment_list: false}) }
  scope :with_supplies, -> { includes(:suppliesApply).where.not(supplies_applies: {id: nil}) }
  scope :without_supplies, -> { in_lots.includes(:suppliesApply).where(supplies_applies: {id: nil}) }
  scope :active, -> { where(drawing_status: ['in_lots', 'not_in_lots', nil]) }

  scope :ordered_by_apply_code, -> { order(:apply_code) }

  belongs_to :scheme
  has_one :confirmation, autosave: true, dependent: :destroy
  has_one :assessment, autosave: true, dependent: :destroy
  has_one :suppliesApply, autosave: true, dependent: :destroy
  has_one :approval, autosave: true, dependent: :destroy
  has_one :settlementApproval, autosave: true, dependent: :destroy
  has_one :remind, autosave: true, dependent: :destroy
  has_one :upload, autosave: true, dependent: :destroy
  has_many :comments, dependent: :destroy

  has_rich_text :resubmit_remark

  before_validation :trim_values
  after_create :pad_companies_tax_number
  after_create :generate_apply_code

  validates :owner_name, presence: { message: I18n.t('company_name_in_m1_required') }
  validates :owner_tax_number, presence: { message: I18n.t('taxpayer_number_required') }
  validates :companies_staff_number, presence: { message: '僱員總數不能留空' }
  validates :companies_macau_staff_number, presence: { message: '僱員人數(澳門居民)不能留空' }
  validates :responsible_officers_name, presence: { message: I18n.t('person_in_charge_of_the_enterprise_name_required') }
  validates :responsible_officers_tel, presence: { message: '企業負責人本澳流動電話不能留空' }
  validates :companies_tax_number, presence: { message: I18n.t('venue_registration_number_required') }
  validates :companies_name, presence: { message: '場所名稱不能留空' }
  validates :companies_address, presence: { message: '場所地址不能留空' }
  validates :shop_housing_number, presence: { message: '物業登記證明(查屋紙)參考編號' }

  #validates :companies_tax_number, uniqueness: { message: I18n.t('taxpayer_number_exist_already') }
  validates :responsible_officers_tel, format: { with: /\A\d{8}\z/, message: I18n.t('person_in_charge_phone_number_format_incorrect') }

  def to_s
    "#{apply_code} #{companies_name}"
  end

  def self.search query
    query = query.strip
    distinct.where("apply_code = :exact_query or companies_name like :query or owner_tax_number = :exact_query",
    exact_query: query,
    query: "%#{query}%")
  end

  def generate_new_valid_code
    valid_code = 6.times.map{ rand 10 }.join

    self.update_attribute(:valid_code, valid_code)
    self.update_attribute(:valid_code_sent_at, DateTime.now)
  end

  def valid_code_correct? valid_code
    return false if valid_code.size != 6
    self.valid_code == valid_code
  end

  def valid_code_expired?
    !self.valid_code_sent_at or self.valid_code_sent_at < 5.minutes.ago
  end

  def encrypted_mobile
    if self.responsible_officers_tel.blank? or !"#{self.responsible_officers_tel}".match(/\d{8}/)
      return "流動電話格式不正確 Mobile tel format incorrect"
    end

    "****" + self.responsible_officers_tel[4..8]
  end

  def no_upload_yet?
    self.upload.nil?
  end

  private

  def trim_values
    attributes.each do |attr_name, value|
      self[attr_name] = value.strip if value.is_a?(String)
    end
  end

  def pad_companies_tax_number
    self.companies_tax_number = companies_tax_number.rjust(6, '0')
  end

  def generate_apply_code
    self.apply_code = Scheme.current.case_number_prefix + "#{id.to_s.rjust(5, '0')}"
    save
  end

  def attachment_exceeding_file_upload_limit
    if [m1_attachments.count, id_card_attachments.count ,photos_attachments.count, other_attachments.count].sum >= MAX_ATTACHMENT_COUNT
      errors.add(:base, I18n.t('exceeding_file_upload_limit'))
    end
  end

end
