class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :trackable,
         :rememberable, :validatable, :lockable

  scope :supplies_only, -> { where(is_supplies: true) }

  has_many :supplies_apply
  has_many :user_assessments
  has_many :assessments, through: :user_assessments

  def to_s
    email.split('@')[0]
  end

  def is_supplies?
    is_supplies
  end

  def is_management?
    email == "<EMAIL>" || email == "<EMAIL>" || is_sm?
  end

  def is_thomas?
    email == "<EMAIL>"
  end

  def is_fin?
    email.starts_with? "fin-"
  end

  def is_ist?
    email.starts_with? "ist-"
  end

  def is_sm?
    email == "<EMAIL>"
  end

  def is_ddg?
    email == "<EMAIL>"
  end

  def is_dg?
    email == "<EMAIL>"
  end

  def is_viewer?
    email == "<EMAIL>"
  end

  def is_only_assessments?
    email == "<EMAIL>"
  end

  def able_delete_attachments?
    !is_supplies? && !is_fin? && !is_management? && !is_viewer?
  end

end
