class ApplicationController < ActionController::Base

  before_action :authenticate_user!
  before_action :redirect_if_account_is_supplies, unless: :devise_controller?
  before_action :redirect_if_account_is_assessments_only, unless: :devise_controller?

  before_action :set_start_time
  before_action :set_locale

  include Pagy::Backend
  require 'pagy/extras/array'

  def set_start_time
    @start_time = Time.now.to_f
  end

  def set_locale
    if params[:locale] && I18n.available_locales.include?( params[:locale].to_sym )
      session[:locale] = params[:locale]
    end

    I18n.locale = session[:locale] || I18n.default_locale
  end

  def redirect_if_account_is_supplies
    if user_signed_in? && current_user.is_supplies?
      redirect_to supplies_applies_path and return
    end
  end

  def redirect_if_account_is_assessments_only
    if user_signed_in? && current_user.is_only_assessments?
      redirect_to upload_only_list_assessments_path and return
    end
  end

  def selected_scheme_id
    session[:scheme_id].nil? ? Scheme.current.id : session[:scheme_id]
  end

end
