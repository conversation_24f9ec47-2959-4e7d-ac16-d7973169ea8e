class CommentsController < ApplicationController

  def create
    @apply = Apply.find params[:apply_id]
    @comment = Comment.new(comment_params)
    @comment.apply = @apply
    @comment.user = current_user

    respond_to do |format|
      if @comment.save
        format.html { redirect_to detail_apply_path(@apply), notice: 'Comment was successfully created.' }
        format.json { render :show, status: :created, location: @comment }
      else
        format.html { redirect_to detail_apply_path(@apply), alert: 'Comment unabled to be created.' }
        format.json { render json: @comment.errors, status: :unprocessable_entity }
      end
    end
  end


  private
    # Only allow a list of trusted parameters through.
    def comment_params
      params.require(:comment).permit(:content)
    end

end