class AppliesController < ApplicationController

  layout "layouts/application", only: [:all, :edit, :search, :late, :more, :detail, :drawing_seed, :drawing, :confirm_drawing_seed]
  before_action :set_apply, only: %i[ show edit update destroy late late_update more give_up pass no_pass detail hide_attachment status_to_need_resubmit audit_status_to_approved audit_status_to_question audit delete_attachment]

  skip_before_action :authenticate_user!, only: [:new, :start, :create, :renew, :recreate, :index, :query, :resubmit, :resubmit_update, :finished, :upload, :info, :rights]

  before_action :redirect_if_account_not_management, only: [:audit, :audit_index, :audit_status_to_approved, :audit_status_to_question ]
  before_action :redirect_if_not_in_apply_duration, only: [:new, :start, :create, :renew, :recreate, :upload]

  # GET /applies or /applies.json
  def index
  end

  # For frontend user query apply status
  def query
    next_step = params[:next_step]
    if next_step == "check_m1_and_captcha"

      if params[:owner_tax_number].blank?
        render "query" and return
      end

      if !verify_rucaptcha?
        redirect_to query_applies_path, alert: I18n.t('incorrent_verfication_code') and return
      end

      owner_tax_number = params[:owner_tax_number].strip
      @apply = Apply.where(owner_tax_number: owner_tax_number).first

      unless @apply.nil?
        session[:owner_tax_number] = owner_tax_number

        unless SmsRecord.has_quota? @apply.responsible_officers_tel
          @notice = t('excessive_sms')
          render "_valid_code" and return
        end

        #sending valid sms
        @apply.generate_new_valid_code
        validCodeSms = Api::ValidCodeSms.new(@apply.responsible_officers_tel, @apply.valid_code)
        #validCodeSms.send()

        #recording send sms record
        SmsRecord.record(@apply.responsible_officers_tel, @apply.apply_code, validCodeSms.message)

        render "_valid_code" and return
      end

      redirect_to query_applies_path, alert: I18n.t('no_matching_taxpayer_number_record')
    end

    if next_step == "check_valid_code"
      if session[:owner_tax_number].nil?
        redirect_to apply_path and return
      end

      valid_code = params[:valid_code]
      @apply = Apply.where(owner_tax_number: session[:owner_tax_number]).first

      # check is try valid code times over today's limit
      ValidCodeRecord.increase @apply.apply_code
      if ValidCodeRecord.overlimit? @apply.apply_code
        @notice = t('valid_code_try_times_over')
        render "_valid_code" and return
      end

      if @apply.valid_code_expired? or !@apply.valid_code_correct?(valid_code)
        @notice = t('valid_code_incorrect')
        render "_valid_code" and return
      end

      #reset today's try valid code count
      ValidCodeRecord.resetlimit @apply.apply_code
      session[:responsible_officers_tel] = @apply.responsible_officers_tel

      redirect_to finished_applies_path and return
    end
  end

  # GET /applies/1 or /applies/1.json
  def show
  end

  # GET /applies/new
  def new
    render "_new_required_document" and return
  end

  def start
    next_step = params[:next_step]

    if next_step == "input_numbers"
      if !verify_rucaptcha?
        render "_new_required_document" and return
      end

      render "_new_input_number" and return
    elsif next_step == "forms"
      #owner_tax_number = params[:owner_tax_number].strip.gsub(/\s+/, "")
      #companies_tax_number = params[:companies_tax_number].strip.gsub(/\s+/, "").rjust(6, "0")

      @apply = Apply.new
      #@apply.owner_tax_number = owner_tax_number
      #@apply.companies_tax_number = companies_tax_number
      render "_new_apply_form" and return
    end

    render "_new_required_document"
  end

  # GET /applies/1/edit
  def edit
  end

  # POST /applies or /applies.json
  def create
    @scheme = Scheme.current
    @apply = @scheme.applies.create(apply_params)
    @apply.confirmation = Confirmation.new

    if @apply && @apply.valid?

      session[:owner_tax_number] = @apply.owner_tax_number
      session[:responsible_officers_tel] = @apply.responsible_officers_tel

      if !SmsRecord.has_quota? @apply.responsible_officers_tel
        redirect_to finished_applies_path, alert: t('excessive_sms') and return
      end

      begin
        applySuccessSms = Api::ApplySuccessSms.new(@apply.responsible_officers_tel, @apply.apply_code)
        #applySuccessSms.send()

        #recording send sms record
        SmsRecord.record(@apply.responsible_officers_tel, @apply.apply_code, applySuccessSms.message)
      rescue => error
        redirect_to finished_applies_path, alert: t('failed_send_sms') and return
      end

      redirect_to finished_applies_path and return
    else
      render "_new_apply_form", apply: @apply, notice: @apply.errors and return
    end
  end

  # PATCH/PUT /applies/1 or /applies/1.json
  def update
    if @apply.update(apply_params)
      redirect_to edit_apply_path(@apply), notice: 'Apply was successfully updated.'
    else
      redirect_to edit_apply_path(@apply), alert: @apply.errors
    end
  end

  def all
    per_page = params[:per_page]
    per_page = 30 if per_page.blank?

    @title = params[:status].present? ? params[:status] : "所有申請"
    @title = params[:stage].present? ? params[:stage] : "所有申請"

    if params[:stage].present?
      @pagy, @applies = pagy(Apply.by_scheme(selected_scheme_id).active.by_stage(params[:stage]).includes(:confirmation).ordered_by_apply_code, items: per_page)
    elsif params[:status] == "快速確認"
      @apply = Apply.by_scheme(selected_scheme_id).registered.sample
      if @apply
        @certified_shop_status = Api::CertifiedShop.getStatus @apply.companies_tax_number
        @shop_count = Apply.by_scheme(selected_scheme_id).where(owner_tax_number: @apply.owner_tax_number).count
      end
      render "_fast_check_list" and return
    elsif params[:status] == "已放棄"
      @pagy, @applies = pagy(Apply.by_scheme(selected_scheme_id).give_up.includes(:confirmation).ordered_by_apply_code, items: per_page)
    elsif params[:status] == "已通過申請"
      @pagy, @applies = pagy(Apply.by_scheme(selected_scheme_id).passed.includes(:confirmation).ordered_by_apply_code, items: per_page)
    elsif params[:status] == "需再確認申請"
      @pagy, @applies = pagy(Apply.by_scheme(selected_scheme_id).no_passed.includes(:confirmation).ordered_by_apply_code, items: per_page)
    elsif params[:status] == "需補交文件"
      @pagy, @applies = pagy(Apply.by_scheme(selected_scheme_id).need_resubmit.includes(:confirmation).ordered_by_apply_code, items: per_page)
    elsif params[:status] == "未有供應商申請"
      @pagy, @applies = pagy(Apply.by_scheme(selected_scheme_id).without_supplies.ordered_by_apply_code, items: per_page)
    elsif params[:status] == "已有供應商申請"
      @pagy, @applies = pagy(Apply.by_scheme(selected_scheme_id).with_supplies.ordered_by_apply_code, items: per_page)
    else
      @pagy, @applies = pagy(Apply.by_scheme(selected_scheme_id).includes(:confirmation).ordered_by_apply_code, items: per_page)
    end

  end

  def all_detailed
    @applies = Apply.all.includes(:confirmation)

    render layout: 'layouts/management'
  end

  #for 同一納稅人編號多次申請
  def renew
    next_step = params[:next_step]

    @first_apply = Apply.where(owner_tax_number: session[:owner_tax_number]).first
    redirect_to new_apply_path and return if @first_apply.nil?

    @apply = Apply.new
    @apply.owner_name = @first_apply.owner_name
    @apply.owner_tax_number = @first_apply.owner_tax_number

    if next_step == "forms"

      owner_tax_number = params[:owner_tax_number].strip.gsub(/\s+/, "")
      companies_tax_number = params[:companies_tax_number].strip.gsub(/\s+/, "")

      @apply.companies_tax_number = companies_tax_number

      if AllAppliesRecord.past_appied?(owner_tax_number, companies_tax_number)
        flash[:message] = t('company_tax_number_register_before', companies_tax_number: companies_tax_number)
        render "_renew_message" and return
      end

      if Apply.where(scheme_id: Scheme.current.id).where(companies_tax_number: companies_tax_number).exists?
        flash[:message] = t('company_tax_number_register_duplicate', companies_tax_number: companies_tax_number)
        render "_renew_message" and return
      end

      render "_renew_apply_form" and return
    end

    render "_renew_input_number" and return
  end

  #for 同一納稅人編號多次申請
  def recreate
    @first_apply = Apply.where(owner_tax_number: session[:owner_tax_number]).first
    redirect_to finished_applies_path and return if @first_apply.nil?

    @scheme = Scheme.current
    @apply = @scheme.applies.new(apply_params)

    copy_owner_data_from_first_apply(@apply, @first_apply)

    @apply.current_stage = Stage.getFirstStageName
    @apply.confirmation = Confirmation.new

    if @apply.valid? && @apply.save
      redirect_to finished_applies_path, notice: t('submission_completed') and return
    else
      render "_renew_apply_form", apply: @apply, notice: @apply.errors and return
    end
  end

  def finished
    if session[:owner_tax_number].nil? || session[:responsible_officers_tel].nil?
      redirect_to applies_path and return
    end

    @applies = Apply.where(owner_tax_number: session[:owner_tax_number])
    .where(responsible_officers_tel: session[:responsible_officers_tel])
  end

  def upload
    id = params[:id]

    if session[:owner_tax_number].nil? || session[:responsible_officers_tel].nil?
      redirect_to applies_path and return
    end

    @apply = Apply.where(owner_tax_number: session[:owner_tax_number])
    .where(responsible_officers_tel: session[:responsible_officers_tel])
    .where(id: id).first

    redirect_to finished_applies_path and return if @apply.nil?

    @upload = Upload.new
  end

  def search
    @applies = Apply.by_scheme(selected_scheme_id).includes(:confirmation).search params[:search]
  end

  def late
  end

  def more
  end


  def detail
    @certified_shop_status = Api::CertifiedShop.getStatus @apply.companies_tax_number
  end

  #for management confirm before drawing
  def audit_index

    per_page = params[:per_page]
    per_page = 30 if per_page.blank?

    if params[:audit_status] == "approved"
      @pagy, @applies = pagy(Apply.audit_approved, items: per_page)
      @page_title = "已通過審核申請"
    elsif params[:audit_status] == "question"
      @pagy, @applies = pagy(Apply.audit_question, items: per_page)
      @page_title = "存疑問申請"
    else
      @pagy, @applies = pagy(Apply.managment_waiting_approved, items: per_page)
      @page_title = "待審核申請"
    end

    render layout: 'layouts/management'
  end

  def audit
    render layout: 'layouts/management'
  end

  def late_update
    if @apply.update(late_submit_params)
      if late_submit_params[:is_need_resubmit_attachments] && @apply.last_resubmit_date.nil?
        #7天內需補交
        @apply.update_attribute(:last_resubmit_date, 7.days.from_now.to_date)
      end

      redirect_to late_apply_path(@apply), notice: 'Apply was successfully updated.'
    else
      redirect_to late_apply_path(@apply), alert: @apply.errors
    end
  end

  def info
  end

  def rights
  end

  def status_to_need_resubmit
    @apply.need_resubmit!
    redirect_to late_apply_path(@apply), notice: '商戶申請狀態已更改為需補交申請文件, 請選擇需補交文件' and return
  end

  def resubmit
    @apply = Apply.where(
        owner_tax_number: session[:owner_tax_number],
        responsible_officers_tel: session[:responsible_officers_tel],
        id: params[:id])
        .take

    redirect_to applies_path and return if @apply.nil?
    redirect_to applies_path and return if @apply.over_last_resubmit_date?
  end

  def resubmit_update
    @apply = Apply.where(
        owner_tax_number: session[:owner_tax_number],
        responsible_officers_tel: session[:responsible_officers_tel],
        id: params[:id])
        .take

    redirect_to applies_path and return if @apply.nil?

    if @apply.update(resubmit_apply_params)
        @apply.update_attribute(:is_new_resubmit, true)
        @apply.update_attribute(:is_need_resubmit_attachments, false)
        redirect_to finished_applies_path, notice: t('submitting_missing_files_completed') and return
    else
        render "resubmit", apply: @apply, alert: @apply.errors and return
    end
  end

  def hide_attachment
    @attachment = ActiveStorage::Attachment.find(params[:upload_id])
    @attachment.update(visible: false)

    redirect_to detail_apply_path(@apply), notice: "隱藏成功" and return
  end

  private

    def redirect_if_not_in_apply_duration
      unless Scheme.current.in_apply_duration?
        redirect_to root_path and return
      end
    end

    def redirect_if_account_not_management
      if user_signed_in? && !current_user.is_management?
        redirect_to all_applies_path and return
      end
    end

    # Use callbacks to share common setup or constraints between actions.
    def set_apply
      @apply = Apply.unscope(where: :scheme_id).find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def apply_params
      params.require(:apply).permit(
        :owner_name,
        :owner_tax_number,
        :owner_so_number,
        :owner_is_personal,
        :owner_is_legal,
        :owner_is_others,
        :owner_others_description,
        :companies_staff_number,
        :companies_macau_staff_number,
        :is_industry_catering,
        :is_industry_retail,
        :responsible_officers_name,
        :responsible_officers_tel,
        :responsible_officers_title,
        :responsible_officers_email,
        :companies_name,
        :companies_tax_number,
        :companies_address,
        :shop_housing_number,
        :is_apply_type_2,
        :is_agree_data_collect
      )
    end

    def resubmit_apply_params
      params.require(:apply).permit(
        :apply_form_attachments => [],
        :id_card_attachments => [],
        :m1_attachments => [],
        :business_proof_attachments => [],
        :operating_license_attachments => [],
        :contribution_receipt_attachments => [],
        :electronic_payment_attachments => [],
        :certified_shop_attachments => [],
        :development_plans_attachments => [],
        :photos_attachments => [],
        :owner_resume_attachments => [],
        :online_plan_attachments => []
      )
    end

    def late_submit_params
      params.require(:apply).permit(
        :is_need_resubmit_attachments,
        :is_need_resubmit_m1,
        :is_need_resubmit_m1_backside,
        :is_need_resubmit_id_card,
        :is_need_resubmit_photos,
        :is_need_resubmit_certified_shop,
        :is_need_resubmit_other,
        :last_resubmit_date,
        :resubmit_remark,
      )
    end

end
