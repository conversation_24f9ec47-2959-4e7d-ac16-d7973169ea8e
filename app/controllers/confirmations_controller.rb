class ConfirmationsController < ApplicationController

  before_action :set_apply

  def edit
  end

  def update
    if @apply.confirmation.update(confirmation_params)
      redirect_to edit_apply_confirmation_path(@apply), notice: 'Apply Confirmation was successfully updated.'
    else
      redirect_to edit_apply_path(@apply), notice: @apply.apply_confirmation.errors
    end
  end

  def status_to_confirmed
    @apply.confirmed!
    @apply.confirmation.update_column(:is_confirm_everything, true)
    @apply.timelines.create(name: "確認提交文件", start_at: Date.today)
    redirect_to edit_apply_confirmation_path(@apply, @apply.confirmation) and return
  end

  private

  def set_apply
    @apply = Apply.unscope(where: :scheme_id).find params[:apply_id]
  end

  def confirmation_params
    params.require(:confirmation).permit(
      :is_application_pass,
      :is_m1_pass,
      :is_id_card_pass,
      :is_photos_pass,
      :is_other_doucments_pass,
      :is_certified_shop_pass,
      :application_remark,
      :m1_remark,
      :id_card_remark,
      :photos_remark,
      :other_document_remark,
      :certified_shop_remark,
      :is_first_apply,
      :is_tax_registered,
      :is_improvable,
      :is_employee_number_not_over,
      :is_operating,
      :is_macau_shop,
      :is_confirm_everything,
      :ist_follower,
      :ecm_follower,
      :is_not_reveal_in_managment_list,
      :restaurant_category,
      :is_excluded_from_drawing,
      :is_confirmed_certified_shop
    )

  end

end
