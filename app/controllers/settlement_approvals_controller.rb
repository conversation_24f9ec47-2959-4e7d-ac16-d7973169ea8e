class SettlementApprovalsController < ApplicationController
  layout :approvals_layout

  def index
    per_page = params[:per_page]
    per_page = 30 if per_page.blank?

    @title = params[:status]

    if params[:status] == "需處理審批"
      @settlement_approvals = SettlementApproval.by_scheme(selected_scheme_id).problems
    elsif params[:status] == "未完成結算審批"
      @settlement_approvals = SettlementApproval.by_scheme(selected_scheme_id).finished(false)
    elsif params[:status] == "完成結算審批"
      @settlement_approvals = SettlementApproval.by_scheme(selected_scheme_id).finished(true)
    else
      @settlement_approvals = SettlementApproval.by_scheme(selected_scheme_id).getWaitingApprovalsBaseOnUser(current_user)
    end

    @pagy, @settlement_approvals = pagy(@settlement_approvals.includes(:apply), items: per_page)
    @selected_scheme_id = selected_scheme_id
  end

  def handle
    raise "error" unless user_signed_in?

    @settlement_approval = SettlementApproval.find params[:id]

    if current_user.is_thomas?
      if params['commit'] == "同意資格"
        @settlement_approval.approved_by_manager = true
      elsif params['commit'] == "不同意資格"
        @settlement_approval.approved_by_manager = false
      end

      @settlement_approval.comment_by_manager = params['settlement_approval']['comment_by_manager']
      @settlement_approval.approved_by_manager_at = DateTime.current
      @settlement_approval.save
    end

    if current_user.is_sm?
      if params['commit'] == "同意資格"
        @settlement_approval.approved_by_sm = true
      elsif params['commit'] == "不同意資格"
        @settlement_approval.approved_by_sm = false
      end

      @settlement_approval.comment_by_sm = params['settlement_approval']['comment_by_sm']
      @settlement_approval.approved_by_sm_at = DateTime.current
      @settlement_approval.save
    end

    if current_user.is_fin?
      if params['commit'] == "同意結算"
        @settlement_approval.verified_by_fin = true
      elsif params['commit'] == "不同意結算"
        @settlement_approval.verified_by_fin = false
      end

      @settlement_approval.comment_by_fin = params['settlement_approval']['comment_by_fin']
      @settlement_approval.verified_by_fin_at = DateTime.current
      @settlement_approval.save
    end

    if @current_user.is_ddg?
      if params['commit'] == "同意資格"
        @settlement_approval.approved_by_ddg = true
      elsif params['commit'] == "不同意資格"
        @settlement_approval.approved_by_ddg = false
      end

      @settlement_approval.comment_by_ddg = params['settlement_approval']['comment_by_ddg']
      @settlement_approval.approved_by_ddg_at = DateTime.current
      @settlement_approval.save
    end

    if current_user.is_dg?
      if params['commit'] == "同意資格"
        @settlement_approval.approved_by_dg = true
      elsif params['commit'] == "不同意資格"
        @settlement_approval.approved_by_dg = false
      end

      @settlement_approval.comment_by_dg = params['settlement_approval']['comment_by_dg']
      @settlement_approval.approved_by_dg_at = DateTime.current
      @settlement_approval.save
    end

    redirect_to process_form_settlement_approval_path(@settlement_approval), notice: "審批完成"
  end

  def process_form
    @settlement_approval = SettlementApproval.find(params[:id])
    @apply = @settlement_approval.apply
    @supplies_apply = @apply.suppliesApply
  end

  private

  def approvals_layout
    @current_user.is_management? || @current_user.is_fin? || @current_user.is_viewer? ? "management" : "application"
  end

end