class DrawingsController < ApplicationController
  layout "layouts/application"

  def all
    per_page = params[:per_page]
    per_page = 30 if per_page.blank?

    if params[:status] == "已中籤"
      @pagy, @applies = pagy(Apply.by_scheme(selected_scheme_id).includes(:confirmation).in_lots.order(drawing_position: :asc), items: per_page)
    elsif params[:status] == "未中籤"
      @pagy, @applies = pagy(Apply.by_scheme(selected_scheme_id).includes(:confirmation).not_in_lots.order(drawing_position: :asc), items: per_page)
    elsif params[:status] == "已放棄"
      @pagy, @applies = pagy(Apply.by_scheme(selected_scheme_id).includes(:confirmation).give_up.order(drawing_position: :asc), items: per_page)
    else
      @pagy, @applies = pagy(Apply.by_scheme(selected_scheme_id).includes(:confirmation).where.not(drawing_position: nil).order(drawing_position: :asc), items: per_page)
    end
  end

  def seed
  end

  # Post by drawing_seed
  def start_draw
    seed = params[:seed].strip

    # saving the order result
    drawing_history = DrawingHistory.new
    drawing_history.seed = seed
    drawing_history.save

    redirect_to action: :list_draw
  end

  def list_draw
    last_draw = DrawingHistory.last
    @seed = last_draw.seed
    @applies = Apply.getDrewAppliesBySeed @seed
  end

  def list_detail
    last_draw = DrawingHistory.last
    @seed = last_draw.seed
    @applies = Apply.getDrewAppliesBySeed @seed
  end

  def confirm_seed
  end

  # Post by confirm_seed
  # Marking drawing status and position
  def confirm_draw
    seed = params[:seed].strip
    @applies = Apply.getDrewAppliesBySeed seed

    #confirm already, not to mark again
    raise "drawing confirm already, cannot draw again" if @applies.first.drawing_status != nil
    quota_in_lots = 750

    ActiveRecord::Base.transaction do
      @applies.each_with_index do |apply, index|
        apply.goNextStage  #已抽籤
        if index < quota_in_lots
          apply.update_attribute(:drawing_status, 'in_lots')
          apply.update(assessment: Assessment.create)
          apply.goNextStage  #培訓及診斷
        else
          apply.update_attribute(:drawing_status, 'not_in_lots')
        end

        apply.update_attribute(:drawing_position, index + 1)
      end
    end

    redirect_to all_applies_path, notice: "抽籤結果成功處理" and return
  end

  def show
    @apply = Apply.find(params[:id])
  end

end