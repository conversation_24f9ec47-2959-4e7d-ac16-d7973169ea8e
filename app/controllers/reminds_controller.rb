class RemindsController < ApplicationController

  def index
    @reminds = Remind.where.not(current_step: "all_finished").includes(:apply).sort_by_days_since_last_step
  end

  def edit
    @remind = Remind.find(params[:id])
  end

  def update
    @remind = Remind.find(params[:id])

    redirect_to edit_remind_path(@remind) and return if params["remind"]["is_go_next_step"] != '1'

    if @remind.update(remind_params)
      @remind.apply.timelines.create(name: I18n.t(@remind.current_step), start_at: @remind.last_step_finished_at)
      @remind.update_to_next_step()
      redirect_to edit_remind_path(@remind), notice: '成功更改阶段'
    else
      redirect_to edit_remind_path(@remind), alert: @remind.errors
    end
  end

  private

  # Only allow a list of trusted parameters through.
  def remind_params
    params.require(:remind).permit(
      :current_step,
      :last_step_finished_at
    )
  end

end