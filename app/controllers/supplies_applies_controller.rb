class SuppliesAppliesController < ApplicationController

  before_action :set_supplies_apply, only: %i[ show edit update cpttm_show cpttm_edit cpttm_update match_index match install_agreement install_agreement_update settlement settlement_update install_report install_report_update generate_install_report delete_attachment ]
  before_action :redirect_if_apply_not_belong_supplies, only: %i[ show edit update settlement settlement_update ]
  skip_before_action :redirect_if_account_is_supplies, except: [ :cpttm_index, :cpttm_show, :cpttm_edit, :match_index, :match ]

  layout "layouts/supplies_application"

  def index
    per_page = params[:per_page]
    per_page = 30 if per_page.blank?

    @pagy, @supplies_applies = pagy(SuppliesApply.open_approval_scheme()
    .for_user(current_user.id)
    .includes(:user, :scheme).order('schemes.id DESC'), items: per_page)
  end

  def cpttm_index
    per_page = params[:per_page]
    per_page = 30 if per_page.blank?

    @pagy, @supplies_applies = pagy(SuppliesApply.by_scheme(selected_scheme_id).includes(:user), items: per_page)
    @repeated_apply_codes = SuppliesApply.by_scheme(selected_scheme_id).group(:apply_code).having("count(*) > 1").pluck(:apply_code)

    render layout: 'layouts/application'
  end

  def submitted_settlement_list
    per_page = params[:per_page]
    per_page = 30 if per_page.blank?

    if params[:status] == "已完成結算"
      @supplies_applies = SuppliesApply.by_scheme(selected_scheme_id).finished_settlement.includes(:user)
    else
      @supplies_applies = SuppliesApply.by_scheme(selected_scheme_id).with_settlement.includes(:user)
    end

    @supplies_applies = @supplies_applies.sort_by{ |model| model.settlement_receipt_attachments.first.created_at }
    render layout: 'layouts/application'
  end

  def cpttm_show
    render layout: 'layouts/application'
  end

  def cpttm_edit
    render layout: 'layouts/application'
  end

  def cpttm_update
    respond_to do |format|
      if @supplies_apply.update(cpttm_update_params)
        @apply = Apply.unscoped.find(@supplies_apply.apply_id)
        if @supplies_apply.is_ready_settlement_approval && @apply.settlementApproval == nil
          @apply.update(settlementApproval: SettlementApproval.create)
        end
        format.html { redirect_to cpttm_edit_supplies_apply_path(@supplies_apply), notice: "更新成功" }
        format.json { render :show, status: :ok, location: @supplies_apply }
      else
        redirect_to cpttm_edit_supplies_apply_path(@supplies_apply), alert: @supplies_apply.errors and return
      end
    end
  end

  def match_index
    @apply_code = params[:apply_code]

    @applies = Apply.unscope(where: :scheme_id).where(apply_code: @apply_code) if params[:apply_code].present?
    render layout: 'layouts/application'
  end

  def match
    @apply_id = params[:apply_id]

    @apply = Apply.unscope(where: :scheme_id).where(id: @apply_id).first
    redirect_to cpttm_index_supplies_applies_path(@supplies_apply), alert: "匹配不成功，沒找到相應申請編號" and return if @apply.nil?

    @apply.update(approval: Approval.create)
    @supplies_apply.apply = @apply
    @supplies_apply.save

    redirect_to cpttm_index_supplies_applies_path(@supplies_apply), notice: "匹配成功" and return
  end

  def install_agreement
    @apply = Apply.unscoped.find(@supplies_apply.apply_id)
    render layout: 'layouts/application'
  end

  def install_agreement_update
    respond_to do |format|
      if @supplies_apply.update(install_agreement_params)
        format.html { redirect_to install_agreement_supplies_apply_path(@supplies_apply), notice: "更新成功" }
        format.json { render :show, status: :ok, location: @supplies_apply }
      else
        redirect_to install_agreement_supplies_apply_path(@supplies_apply), alert: @supplies_apply.errors and return
      end
    end
  end

  def show
    unless current_user.is_supplies
      render layout: 'layouts/application'
    end
  end

  def new
    @supplies_apply = SuppliesApply.new
  end

  def create
    current_submitting_scheme_id = 1
    @scheme = Scheme.find current_submitting_scheme_id
    @supplies_apply = @scheme.supplies_apply.new(supplies_apply_params)
    @supplies_apply.user = current_user

    if @supplies_apply.save
      redirect_to supplies_apply_path(@supplies_apply), notice: "創建成功" and return
    else
      redirect_to new_supplies_apply_path, alert: @supplies_apply.errors and return
    end
  end

  def edit
    unless current_user.is_supplies
      render layout: 'layouts/application'
    end
  end

  def update
    respond_to do |format|
      if @supplies_apply.update(supplies_apply_params)
        format.html { redirect_to supplies_apply_path(@supplies_apply), notice: "更新成功" }
        format.json { render :show, status: :ok, location: @supplies_apply }
      else
        redirect_to edit_supplies_apply_path(@supplies_apply), alert: @supplies_apply.errors and return
      end
    end
  end

  def recommended_applies
    per_page = params[:per_page]
    per_page = 30 if per_page.blank?

    @pagy, @assessments = pagy(Assessment.open_approval_scheme()
    .where(is_open_data_to_supplies: true)
    .includes(apply: :scheme).order('schemes.id DESC, apply.id'), items: per_page)
  end

  def settlement
  end

  def settlement_update
    respond_to do |format|
      if @supplies_apply.update(supplies_apply_params)
        format.html { redirect_to settlement_supplies_apply_path(@supplies_apply), notice: "更新成功" }
        format.json { render :show, status: :ok, location: @supplies_apply }
      else
        redirect_to settlement_supplies_apply_path(@supplies_apply), alert: "failed" and return
      end
    end
  end

  def install_report
    @apply = Apply.unscoped.find(@supplies_apply.apply_id)
    render layout: 'layouts/application'
  end

  def install_report_update
    if @supplies_apply.update(supplies_apply_params)
      redirect_to install_report_supplies_apply_path(@supplies_apply), notice: 'Report was successfully updated.'
    else
      redirect_to install_report_supplies_apply_path(@supplies_apply), alert: @supplies_apply.errors
    end
  end

  def generate_install_report
    @apply = Apply.unscoped.find(@supplies_apply.apply_id)

    if @apply.scheme.case_number_prefix == "2"
      render template: '/supplies_applies/generate_install_report_2024', layout: 'report' and return
    end

    render layout: 'layouts/report'
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_supplies_apply
      @supplies_apply = SuppliesApply.unscope(where: :scheme_id).find(params[:id])
    end

    def redirect_if_apply_not_belong_supplies
      return unless current_user.is_supplies
      redirect_to supplies_applies_path if @supplies_apply.user.id != current_user.id
    end

    # Only allow a list of trusted parameters through.
    def supplies_apply_params
      params.require(:supplies_apply).permit(
        :apply_code,
        :companies_name,
        :companies_tax_number,
        :description,
        :install_report_address,
        :install_report_person_name,
        :install_report_date,
        :install_report_serial_number,
        :is_sent_install_report,
        :quotation_attachments => [],
        :authorization_letter_attachments => [],
        :settlement_receipt_attachments => [],
        :settlement_installment_attachments => [],
        :settlement_training_attachments => [],
        :settlement_online_pay_attachments => [],
        :settlement_others_attachments => [],
        :install_report_code_photos => []
      )
    end

    def install_agreement_params
      params.require(:supplies_apply).permit(
        :is_accept_apply,
        :amounts,
        :report,
        :install_agreement_letter_attachments => []
      )
    end

    def cpttm_update_params
      params.require(:supplies_apply).permit(
        :settlement_report,
        :is_ready_settlement_approval,
        :is_finish_settlement,
        :settlement_checking_photos => []
      )
    end
end