class AssessmentsController < ApplicationController

  before_action :set_assessment, only: %i[ edit update report report_update generate_report upload_checking update_upload_checking ]
  skip_before_action :redirect_if_account_is_assessments_only

  layout :assessments_layout

  def all
    per_page = params[:per_page]
    per_page = 30 if per_page.blank?

    @title = params[:status].present? ? params[:status] : "評估 & 培訓"

    if params[:status] == "未完成培訓"
      @assessment = Assessment.by_scheme(selected_scheme_id).yet_finished_training
    elsif params[:status] == "已完成培訓"
      @assessment = Assessment.by_scheme(selected_scheme_id).finished_training
    elsif params[:status] == "缺席培訓"
      @assessment = Assessment.by_scheme(selected_scheme_id).absent_training
    elsif params[:status] == "已完成診斷"
      @assessment = Assessment.by_scheme(selected_scheme_id).finished_report
    elsif params[:status] == "未完成診斷"
      @assessment = Assessment.by_scheme(selected_scheme_id).yet_finished_report
    else
      @assessment = Assessment.by_scheme(selected_scheme_id)
    end

    @pagy, @assessments = pagy(@assessment.includes(:apply).order(is_open_data_to_supplies: :desc).order('apply.position'), items: per_page)
  end

  def edit
    @apply = @assessment.apply
  end

  def update
    if @assessment.update(assessment_params)
      redirect_to edit_apply_assessment_path(@assessment.apply, @assessment), notice: 'Assessment was successfully updated.'
    else
      redirect_to edit_apply_assessment_path(@assessment.apply, @assessment), alert: @assessment.errors
    end
  end

  def create_suggestion
    @suggestion = Suggestion.create(suggestion_params)

    respond_to do |format|
      if @suggestion.save
        format.js
      else
        format.js { render 'error' }
      end
    end
  end

  def report
    @assessment.last_submit_date ||= "0000年00月00日"
    if @assessment.analysis_text.nil?
      if @assessment.apply.scheme.case_number_prefix == "1"

        @assessment.analysis_text = <<~TEXT
        根據訪談，貴企業已成立 «shop_age» ，以 «shop_type» 為主要業務，企業目前有 «full_time_employee» 名僱員 ，年齡在 «employee_avg_age» 歲之間。

        由於  貴企業期望之數字化改革方向是 “ «business_expectations» ” ，根據目前的企業數字化程度及軟硬件配套判斷，建議考慮以下方面：


        預期在進行上述優化後，企業的經營狀況將會得到一定程度的改善。
        TEXT

        @assessment.analysis_text = @assessment.analysis_text.sub(" «shop_age» ", @assessment.shop_age) if @assessment.shop_age.present?
        @assessment.analysis_text = @assessment.analysis_text.sub(" «shop_type» ", @assessment.shop_type) if @assessment.shop_type.present?
        @assessment.analysis_text = @assessment.analysis_text.sub(" «full_time_employee» ", @assessment.full_time_employee) if @assessment.full_time_employee.present?
        @assessment.analysis_text = @assessment.analysis_text.sub(" «employee_avg_age» ", @assessment.employee_avg_age) if @assessment.employee_avg_age.present?
        @assessment.analysis_text = @assessment.analysis_text.sub(" «business_expectations» ", @assessment.business_expectations) if @assessment.business_expectations.present?

      else

        @assessment.analysis_text = <<~TEXT
        根據訪談，貴企業已成立 «shop_age» ，以 «shop_type» 為主要業務，主要客源來自 «client_target» 。

        目前，貴企業已透過 «it_support» 進行日常運營，目前企業遇到的困難主要來自 «business_diff» ，而貴企業期望之數字化改革方向是 “ «business_expectations» ” ，根據目前的企業數字化程度及軟硬件配套判斷，建議考慮以下方面：


        預期在進行上述優化後，企業的經營狀況將會得到一定程度的改善。
        TEXT

        @assessment.analysis_text = @assessment.analysis_text.sub(" «shop_age» ", @assessment.shop_age) if @assessment.shop_age.present?
        @assessment.analysis_text = @assessment.analysis_text.sub(" «shop_type» ", @assessment.shop_type) if @assessment.shop_type.present?
        @assessment.analysis_text = @assessment.analysis_text.sub(" «client_target» ", @assessment.client_target) if @assessment.client_target.present?
        @assessment.analysis_text = @assessment.analysis_text.sub(" «it_support» ", @assessment.it_support) if @assessment.it_support.present?
        @assessment.analysis_text = @assessment.analysis_text.sub(" «business_diff» ", @assessment.business_diff) if @assessment.business_diff.present?
        @assessment.analysis_text = @assessment.analysis_text.sub(" «business_expectations» ", @assessment.business_expectations) if @assessment.business_expectations.present?
      end
    end

    @apply = @assessment.apply
    @suggestion = Suggestion.new
  end

  def report_update
    if @assessment.update(assessment_params)
      @assessment.apply.goNextStage if @assessment.is_open_data_to_supplies && @assessment.apply.stage.equal?("培訓及診斷")
      redirect_to report_apply_assessment_path(@assessment.apply, @assessment), notice: 'Report was successfully updated.'
    else
      redirect_to report_apply_assessment_path(@assessment.apply, @assessment), alert: @assessment.errors
    end
  end

  def generate_report
    @apply = @assessment.apply
    @assessment.last_submit_date ||= "0000年00月00日"

    if @apply.scheme.case_number_prefix == "2"
      render template: '/assessments/generate_report_2024', layout: 'report' and return
    end

    render layout: 'report'
  end

  def upload_checking
    @apply = @assessment.apply
  end

  def update_upload_checking
    respond_to do |format|
      if @assessment.update(upload_checking_params)
        format.html { redirect_to upload_checking_apply_assessment_path(@assessment.apply, @assessment), notice: "更新成功" }
        format.json { render :show, status: :ok, location: @assessment }
      else
        redirect_to upload_checking_apply_assessment_path(@assessment.apply, @assessment), alert: @assessment.errors and return
      end
    end
  end

  def upload_only_list
    per_page = params[:per_page]
    per_page = 30 if per_page.blank?

    @pagy, @assessments = pagy(Assessment.by_scheme(selected_scheme_id).includes(:apply).order(is_open_data_to_supplies: :desc).order('apply.position'), items: per_page)
  end

  private

  def assessment_params
    params.require(:assessment).permit(
      :assessment_date,
      :is_finished_assessment,
      :assessment_remark,
      :training_date,
      :is_finished_training,
      :is_absent_training,
      :training_remark,
      :analysis_text,
      :catering_supplies_name,
      :catering_supplies_tel,
      :company_strategy,
      :analysis_date,
      :last_submit_date,
      :is_submit_quotation,
      :is_open_data_to_supplies,
      :report_scan_attachments => [],
      user_ids: []
    )
  end

  def upload_checking_params
    params.require(:assessment).permit(:checking_photos_attachments => [])
  end

  def suggestion_params
    params.require(:suggestion).permit(:content)
  end

  def set_assessment
    @assessment = Assessment.find(params[:id])
  end

  def assessments_layout
    @current_user.is_only_assessments? ? "assessment_only" : "application"
  end

end