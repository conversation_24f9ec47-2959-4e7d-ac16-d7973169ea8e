class ApprovalsController < ApplicationController
  layout :approvals_layout

  def index
    per_page = params[:per_page]
    per_page = 30 if per_page.blank?

    @title = params[:status].present? ? params[:status] : "待審批申請"

    if params[:status] == "所有審批"
      @approvals = Approval.by_scheme(selected_scheme_id).all
    elsif params[:status] == "需處理審批"
      @approvals = Approval.by_scheme(selected_scheme_id).problems
    elsif current_user.is_thomas?
      @approvals = Approval.by_scheme(selected_scheme_id).for_manager_approval
    elsif current_user.is_sm?
      @approvals = Approval.by_scheme(selected_scheme_id).for_sm_approval
    elsif current_user.is_ddg?
      @approvals = Approval.by_scheme(selected_scheme_id).for_ddg_approval
    elsif current_user.is_dg?
      @approvals = Approval.by_scheme(selected_scheme_id).for_dg_approval
    else
      @approvals = Approval.where(id: [nil])  #empty approvals list
    end

    @size = @approvals.size
    @pagy, @approvals = pagy(@approvals.includes(:apply), items: per_page)
    @selected_scheme_id = selected_scheme_id
  end

  def handle
    raise "error" unless user_signed_in?

    @approval = Approval.find params[:id]

    if current_user.is_thomas?
      if params['commit'] == "同意資格"
        @approval.approved_by_manager = true
      elsif params['commit'] == "不同意資格"
        @approval.approved_by_manager = false
      end

      @approval.comment_by_manager = params['approval']['comment_by_manager']
      @approval.approved_by_manager_at = DateTime.current
      @approval.save
    end

    if current_user.is_sm?
      if params['commit'] == "同意資格"
        @approval.approved_by_sm = true
      elsif params['commit'] == "不同意資格"
        @approval.approved_by_sm = false
      end

      @approval.comment_by_sm = params['approval']['comment_by_sm']
      @approval.approved_by_sm_at = DateTime.current
      @approval.save
    end

    if @current_user.is_ddg?
      if params['commit'] == "同意資格"
        @approval.approved_by_ddg = true
      elsif params['commit'] == "不同意資格"
        @approval.approved_by_ddg = false
      end

      @approval.comment_by_ddg = params['approval']['comment_by_ddg']
      @approval.approved_by_ddg_at = DateTime.current
      @approval.save
    end

    if current_user.is_dg?
      if params['commit'] == "同意資格"
        @approval.approved_by_dg = true
      elsif params['commit'] == "不同意資格"
        @approval.approved_by_dg = false
      end

      @approval.comment_by_dg = params['approval']['comment_by_dg']
      @approval.approved_by_dg_at = DateTime.current
      @approval.save
    end

    redirect_to process_form_approval_path(@approval), notice: "審批完成"
  end

  def process_form
    @approval = Approval.find(params[:id])
    @apply = Apply.unscoped.find(@approval.apply_id)
    @supplies_apply = SuppliesApply.unscoped.where(apply_id: @apply.id).first
  end

  private

  def approvals_layout
    @current_user.is_management? || @current_user.is_fin? || @current_user.is_viewer? ? "management" : "application"
  end

end
