module ApplicationHelper

  include Pagy::Frontend

  def nice_date date = nil
    date.respond_to?(:strftime) ? date.strftime("%Y-%m-%d %H:%M:%S") : 'N/A'
  end

  def true_tick(true_or_false)
    return "✔️" if true_or_false
  end

  def true_tick_or_cross(true_or_false)
    return "✔️" if true_or_false
    return "❌"
  end

  def true_tick_large_then_zero(number)
    return "✔️" if number > 0
  end

  def show_audit_icon(status)
    return "✔️" if status == 'audit_approved'
    return "❓" if status == 'audit_question'
    ""
  end

  def show_drawing_status(apply)
    return "✔️" if apply.in_lots?
    return "未在隊列中" if apply.not_in_lots?
    return "放棄申請" if apply.give_up?
  end

  def apply_stage_label(apply)
    if apply.give_up?
      tag.span("已放棄", class: "label secondary")
    else
      tag.span(apply.current_stage, class: "label success")
    end
  end

end
