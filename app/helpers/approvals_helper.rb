module Approvals<PERSON><PERSON><PERSON>

  def approved_mark(bool)
    return "✔️" if bool
    return "❌"
  end

  def approved_flow(approval)
    if approval.approved_by_ddg.nil? && !approval.approved_by_dg.nil?
      return "經理#{approved_mark(approval.approved_by_manager)}→高經#{approved_mark(approval.approved_by_sm)}→DDG（待）→DG#{approved_mark(approval.approved_by_dg)}"
    elsif !approval.approved_by_dg.nil?
      return "經理#{approved_mark(approval.approved_by_manager)}→高經#{approved_mark(approval.approved_by_sm)}→DDG#{approved_mark(approval.approved_by_ddg)}→DG#{approved_mark(approval.approved_by_dg)}"
    elsif !approval.approved_by_ddg.nil?
      return "經理#{approved_mark(approval.approved_by_manager)}→高經#{approved_mark(approval.approved_by_sm)}→DDG#{approved_mark(approval.approved_by_ddg)}→DG（待）"
    elsif !approval.approved_by_sm.nil?
      return "經理#{approved_mark(approval.approved_by_manager)}→高經#{approved_mark(approval.approved_by_sm)}→DDG（待）"
    elsif !approval.approved_by_manager.nil?
      return "經理#{approved_mark(approval.approved_by_manager)}→高經（待）"
    else
      return "經理（待）"
    end
  end

end
