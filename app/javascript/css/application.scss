@import '~foundation-sites/scss/foundation';

@import 'settings';

@import 'foundation-float.min.css';


// Include everything by default. Pick individual components from here instead
// https://github.com/foundation/foundation-sites/blob/develop/scss/foundation.scss#L80



// Time line
@mixin timeline-line($background) {
  background: $background;
  height: 0.5rem;
  float: left;
  position: relative;
}

@mixin timeline-start($background) {
      height: 1rem;
      width: 1rem;
      position: absolute;
      right: 0;
      background: darken($background, 10);
      border-radius: 50%;
      top: -0.3125rem;
      right: -0.625rem;
      z-index: 5;
      span {
        position: absolute;
        font-size: 0.8rem;
        width: 200px;
        right: -90px;
        top: 20px;
        text-align: center;
      }
}


.timeline {
  padding: 3rem 0;
  .active {@include timeline-line(#008b00);}
  .inactive {@include timeline-line(#eee);}
  .start {@include timeline-start(#008b00);}
  .inactive-start {@include timeline-start(#eee);}
}
