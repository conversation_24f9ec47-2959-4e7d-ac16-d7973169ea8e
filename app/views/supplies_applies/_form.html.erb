<style>
  span.error {
    color: red;
  }
</style>


<%= simple_form_for(@supplies_apply) do |f| %>
  <%= f.error_notification %>
  <%= f.error_notification message: f.object.errors[:base].to_sentence if f.object.errors[:base].present? %>

  <div class="form-inputs">
    <%= f.input :companies_name, label: "商戶名稱" %>
    <%= f.input :companies_tax_number, label: "商戶場所登記編號:"%>
    <%= f.input :apply_code, label: "商戶本次計劃申請編號:" %>
    <%= f.input :description, label: "備註:" %>
  </div>

  <br><br>
  <h2>上傳新檔案</h2>
  <br>
  <div>
    <%= f.label :quotation_attachments, "報價單" %>
    <%= f.file_field :quotation_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg" %>
  </div>
  <div>
    <%= f.label :authorization_letter_attachments, "商戶授權書" %>
    <%= f.file_field :authorization_letter_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg" %>
  </div>

  <br><br>

   <%= button_tag(type: 'submit', class: "button") do %>
     儲存
   <% end %>

<% end %>