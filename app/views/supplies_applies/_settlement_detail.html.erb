<div class="columns">
      <h2>已上傳結算檔案</h2>
      <table class="review">
        <tr>
          <th>文件</th>
          <th>備註</th>
        </tr>
        <tr>
          <td>安裝系統發票</td>
          <td>
            <p>(數目: <%= @supplies_apply.settlement_receipt_attachments.count %>) (上傳日期: <%= @supplies_apply.settlement_receipt_attachments.first.created_at.strftime("%Y-%m-%d") if @supplies_apply.settlement_receipt_attachments.first %>)</p>
            <%= render 'applies/detail_attachments', attachments: @supplies_apply.settlement_receipt_attachments, attachments_type: "supplies_apply" %>
          </td>
        </tr>
        <tr>
          <td>安裝系統後的相片／圖片</td>
          <td>
            <p>(數目: <%= @supplies_apply.settlement_installment_attachments.count %>) (上傳日期: <%= @supplies_apply.settlement_installment_attachments.first.created_at.strftime("%Y-%m-%d") if @supplies_apply.settlement_installment_attachments.first %>)</p>
            <%= render 'applies/detail_attachments', attachments: @supplies_apply.settlement_installment_attachments %>
          </td>
        </tr>
        <tr>
          <td>培訓記錄</td>
          <td>
            <p>(數目: <%= @supplies_apply.settlement_training_attachments.count %>) (上傳日期: <%= @supplies_apply.settlement_training_attachments.first.created_at.strftime("%Y-%m-%d") if @supplies_apply.settlement_training_attachments.first %>)</p>
            <%= render 'applies/detail_attachments', attachments: @supplies_apply.settlement_training_attachments %>
          </td>
        </tr>
        <tr>
          <td>申請線上支付的相關文件證明</td>
          <td>
            <p>(數目: <%= @supplies_apply.settlement_online_pay_attachments.count %>) (上傳日期: <%= @supplies_apply.settlement_online_pay_attachments.first.created_at.strftime("%Y-%m-%d") if @supplies_apply.settlement_online_pay_attachments.first %>)</p>
            <%= render 'applies/detail_attachments', attachments: @supplies_apply.settlement_online_pay_attachments %>
          </td>
        </tr>
        <tr>
          <td>其他補充資料</td>
          <td>
            <p>(數目: <%= @supplies_apply.settlement_others_attachments.count %>) (上傳日期: <%= @supplies_apply.settlement_others_attachments.first.created_at.strftime("%Y-%m-%d") if @supplies_apply.settlement_others_attachments.first %>)</p>
            <%= render 'applies/detail_attachments', attachments: @supplies_apply.settlement_others_attachments %>
          </td>
        </tr>
      </table>
  </div>