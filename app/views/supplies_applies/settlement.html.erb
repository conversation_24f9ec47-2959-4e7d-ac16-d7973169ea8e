<style>
  span.error {
    color: red;
  }
</style>

<h1><%= @supplies_apply.companies_name %> 上傳結算資料</h1>

<br><br>
<p>請上傳以下證明文件:</p>
<ul>
  <li>安裝系統發票 ( To 澳門生產力暨科技轉移中心 )</li>
  <li>系統及其相關硬件（倘有）安裝後的相片／圖片，其應清晰顯示已經安裝的機具（倘有）及系統運行狀態。</li>
  <li>已接受培訓的人員名單及其在申請企業內的職位，培訓的時數、日期、時間及接受培訓的地點，培訓記錄須有已接受培訓人員的簽名確認。</li>
  <li>協助企業申請線上支付的相關文件證明。</li>
  <li>其他補充資料（如有），並作說明。</li>
</ul>

<%= simple_form_for @supplies_apply, url: { action: "settlement_update" } do |f| %>
  <%= f.error_notification %>
  <%= f.error_notification message: f.object.errors[:base].to_sentence if f.object.errors[:base].present? %>

  <br><br>
  <h2>上傳結算資料(文件大小上限 10MB)</h2>

  <div>
    <%= f.hidden_field :companies_name %>
  </div>

  <div>
    <%= f.label :settlement_receipt_attachments, "安裝系統發票 ( To 澳門生產力暨科技轉移中心 )" %>
    <%= f.file_field :settlement_receipt_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg" %>
  </div>
  <br>

  <div>
    <%= f.label :settlement_installment_attachments, "安裝系統後的相片／圖片" %>
    <%= f.file_field :settlement_installment_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg" %>
  </div>
  <br>

  <div>
    <%= f.label :settlement_training_attachments, "培訓記錄" %>
    <%= f.file_field :settlement_training_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg" %>
  </div>
  <br>

  <div>
    <%= f.label :settlement_online_pay_attachments, "申請線上支付的相關文件證明" %>
    <%= f.file_field :settlement_online_pay_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg" %>
  </div>
  <br>

  <div>
    <%= f.label :settlement_others_attachments, "其他補充資料" %>
    <%= f.file_field :settlement_others_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg" %>
  </div>
  <br>


  <%= button_tag(type: 'submit', class: "button") do %>
     儲存
  <% end %>

<% end %>

<br><br>
<div class="row expanded">
  <%= render 'supplies_applies/settlement_detail' %>

</div>