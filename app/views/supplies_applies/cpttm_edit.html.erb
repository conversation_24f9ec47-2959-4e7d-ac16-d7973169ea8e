<div class="row expanded">
  <div class="columns large-6">

    <h1>處理結算資料</h1>
      <%= simple_form_for @supplies_apply, url: { action: "cpttm_update" } do |f| %>
        <%= f.error_notification %>
        <%= f.error_notification message: f.object.errors[:base].to_sentence if f.object.errors[:base].present? %>

        <div class="form-inputs">
          <%= f.label :settlement_report, "結算報告內容" %>
          <%= f.text_area :settlement_report, label: "結算報告內容", rows: 5 %>
        </div>

        <div class="form-inputs">
          <%= f.input :is_ready_settlement_approval, label: "確認資料, 進入結算審批階段" %>
        </div><br><br>

        <div class="form-inputs">
          <%= f.input :is_finish_settlement, as: :boolean, label: "已完成結算" %>
        </div><br><br>

        <div>
          <%= f.label :settlement_checking_photos, "上傳結算稽查照片" %>
          <%= f.file_field :settlement_checking_photos, multiple: true, accept: "application/pdf,image/png,image/jpeg" %>
        </div><br><br>


        <%= button_tag(type: 'submit', class: "button") do %>
        儲存
        <% end %>
    <% end %>
  </div>

  <div class="columns large-6">
    <%= render 'checking_photos_detail', supplies_apply: @supplies_apply %>
  </div>
</div>

  <hr>

<div class="row expanded">
  <%= render 'apply_detail' %>
</div>

<div class="row expanded">
  <div class="columns large-6">
    <%= render 'settlement_detail' %>
  </div>

  <%= render 'apply_attachments_detail' %>

</div>