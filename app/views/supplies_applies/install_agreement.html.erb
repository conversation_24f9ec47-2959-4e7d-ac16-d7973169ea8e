<div class="row expanded">
  <div class="columns large-6">

  <h2>處理供應商提交</h2><br>
  <%= simple_form_for @supplies_apply, url: { action: "install_agreement_update" } do |f| %>
      <%= f.error_notification %>
      <%= f.error_notification message: f.object.errors[:base].to_sentence if f.object.errors[:base].present? %>

      <div class="form-inputs">
        <%= f.label :report, "報告內容" %>
        <%= f.text_area :report, label: "報告內容", rows: 5 %>
      </div>

      <div class="form-inputs">
        <%= f.input :amounts, label: "資助金額(上限18000)" %>
      </div>

      <br><br>

      <div class="form-inputs">
        <%= f.input :is_accept_apply, label: "通知供應商, 已發出同意安裝通知書" %>
      </div>
      <br><br>

      <div>
        <%= f.label :install_agreement_letter_attachments, "上傳同意安裝通知書" %>
        <%= f.file_field :install_agreement_letter_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg" %>
      </div>

      <br><br>

      <%= button_tag(type: 'submit', class: "button") do %>
      儲存
      <% end %>

  <% end %>
  <hr>

  </div>
</div>

<%= render 'detail' %>

<br><br><br>
<hr>
<h1>申請資料</h1>
<div class="row expanded">
  <div class="columns large-6">
    <%= render 'applies/show' %>
  </div>
  <div class="columns large-6">
    <%= render 'applies/show_attachments' %>
  </div>
</div>
