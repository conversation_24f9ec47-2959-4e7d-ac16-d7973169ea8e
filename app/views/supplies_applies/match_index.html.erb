<h1>歸納供應商提交 <%= @supplies_apply.companies_name %> 資料</h1>

<br><br>
<%= form_with url: match_index_supplies_apply_path(@supplies_apply), method: :get do |form| %>
  <h4>搜索申請編號: <%= form.text_field :apply_code, value: @apply_code, style: "display: inline; width: 25%;" %> <%= form.submit "Search", class: "button", style: "vertical-align: inherit;" %></h4>
<% end %>

<div class="row">
  <table class="table">
    <thead class="table-dark">
      <tr>
        <th>申請編號</th>
        <th>企業名稱</th>
        <th>企業負責人</th>
        <th>場所登記編號</th>
        <th>已在中籤隊列</th>
        <th></th>
      </tr>
    </thead>
    <% @applies.each do |apply| %>
    <tr>
        <td><%= apply.apply_code %></td>
        <td><%= apply.companies_name %></td>
        <td><%= apply.responsible_officers_name %></td>
        <td><%= apply.companies_tax_number %></td>
        <td><%= show_drawing_status(apply) %></td>
        <td>
          <% if apply.in_lots? %>
            <% if apply.suppliesApply.present? || !apply.assessment.is_open_data_to_supplies %>
              此申請已存在匹配或未完成診斷
            <% else %>
              <%= form_with url: match_supplies_apply_path(@supplies_apply) do |form| %>
                <%= hidden_field_tag :apply_id, apply.id %>
                <%= form.submit "匹配此申請", class: "button", data: {confirm: "確認匹配至此申請?"} %>
              <% end %>
            <% end %>
          <% end %>
        </td>
      </tr>
    <% end %>
  </table>

</div>
