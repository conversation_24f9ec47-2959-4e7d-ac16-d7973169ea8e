<!DOCTYPE html>
<html>
  <head>
    <title>專精特色店資助計劃</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= stylesheet_link_tag 'application', media: 'all', 'data-turbolinks-track': 'reload' %>
    <%= javascript_pack_tag 'application', 'data-turbolinks-track': 'reload' %>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css">

</head>

  <body>

  <div class="app-dashboard shrink-medium">
  <div class="row expanded app-dashboard-top-nav-bar">
    <div class="columns medium-4">
      <button data-toggle="app-dashboard-sidebar" class="menu-icon hide-for-medium"></button>
      <a class="app-dashboard-logo">專精特色店資助計劃</a>
    </div>
    <div class="columns medium-4">
      <div class="app-dashboard-top-bar-actions">
      </div>
    </div>

    <div class="columns medium-4 app-dashboard-top-bar-actions" style="justify-content: right;">
      <span class="login-name"><%= current_user %></span>&nbsp;&nbsp;
      <%= link_to('Logout', destroy_user_session_path, method: :delete, class: "button") %>
    </div>
  </div>

  <div class="app-dashboard-body off-canvas-wrapper">
    <div id="app-dashboard-sidebar" class="app-dashboard-sidebar position-left off-canvas off-canvas-absolute reveal-for-medium" data-off-canvas>
      <div class="app-dashboard-sidebar-title-area">
        <div class="app-dashboard-close-sidebar">
          <h3 class="app-dashboard-sidebar-block-title">Items</h3>
          <!-- Close button -->
          <button id="close-sidebar" data-app-dashboard-toggle-shrink class="app-dashboard-sidebar-close-button show-for-medium" aria-label="Close menu" type="button">
            <span aria-hidden="true"><a href="#"><i class="large fa fa-angle-double-left"></i></a></span>
          </button>
        </div>
        <div class="app-dashboard-open-sidebar">
          <button id="open-sidebar" data-app-dashboard-toggle-shrink class="app-dashboard-open-sidebar-button show-for-medium" aria-label="open menu" type="button">
            <span aria-hidden="true"><a href="#"><i class="large fa fa-angle-double-right"></i></a></span>
          </button>
        </div>
      </div>
      <div class="app-dashboard-sidebar-inner">
        <ul class="menu vertical">
          <li><a href="/dashboard/management">
            <i class="large fa fa-chart-line"></i><span class="app-dashboard-sidebar-text">Dashboard</span>
          </a></li>
          <li><a href="/approvals">
            <i class="large fa fa-pen"></i><span class="app-dashboard-sidebar-text">待審核申請</span>
          </a></li>
          <li><hr></li>
          <li><a href="/settlement_approvals">
            <i class="large fa fa-clipboard-check"></i><span class="app-dashboard-sidebar-text">結算審批</span>
          </a></li>
        </ul>
      </div>
    </div>

    <div class="app-dashboard-body-content off-canvas-content" data-off-canvas-content>
      <div class="main-content">
        <p class="notice"><%= notice %></p>
        <p class="alert"><%= alert %></p>
        <%= yield %>
      </div>

      <center>
        CPTTM &copy; <%= Date.current.year %>.
        <br>
        Page Rendered in <%= sprintf('%.3f', (Time.now.to_f - @start_time) ) %> seconds.
      </center>
    </div>


  </div>
</div>

  <script>
    $('[data-app-dashboard-toggle-shrink]').on('click', function(e) {
      e.preventDefault();
      $(this).parents('.app-dashboard').toggleClass('shrink-medium').toggleClass('shrink-large');
    });

  </script>
  </body>
</html>
