<%= render 'applies/info' %>

<h1><%= @assessment.apply.companies_name %> 上傳稽查照片</h1>


<%= form_with id: "apply_form", url: update_upload_checking_apply_assessment_path, model: @assessment, local: true do |form| %>
<div class="row expanded">

    <div class="columns large-6">
      <br><br>
      <%= form.label :checking_photos_attachments, "上傳稽查照片" %>
      <%= form.file_field :checking_photos_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg" %>

      <br><br>
      <%= form.submit "提交" %>
    </div>

    <div class="columns large-6">
      <%= render 'upload_checking_detail', assessment: @assessment %>
    </div>

</div>
<% end %>