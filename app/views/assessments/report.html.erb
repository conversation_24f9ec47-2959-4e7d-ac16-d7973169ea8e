
<style>

  div#container {
    border: 1px solid #263238;
    padding: 2em;
  }

  div#container h3 {
    margin: 1.2em 0;
  }

  #report_update_assessment_form label {
    font-size: 1.2em;
  }

  table.status-report td {
    border: 1px solid #263238;
  }

  div.suggestions {
    min-height: 20em;
  }

  div.suggestions a {
    margin: 0 1em 1em;
    padding: 1em;
    background-color: #cecece;
    line-height: 1em;
    border-radius: 1.2em;
    display: inline-block;
    color: #212121;
  }

</style>


<%= render 'applies/info' %>

<div class="row">
  <div id="container">

    <h1 class="text-center">“<%= @assessment.apply.scheme.name %>”診斷報告</h1>
    <%= link_to '生成報告', generate_report_apply_assessment_path, class: 'button float-right' %>
    <h2>經營狀態：</h2>

    <%= form_with id: "report_update_assessment_form", url: report_update_apply_assessment_path, model: @assessment, local: true do |form| %>

      <table class="status-report unstriped">
        <tr>
          <td colspan="2"><b>申請編號：</b><%= @assessment.apply.apply_code %></td>
        </tr>
        <tr>
          <td width="45%"><b>場所登記編號：</b><%= @assessment.apply.companies_tax_number %></td>
          <td><b>企業名稱：</b><%= @assessment.apply.companies_name %></td>
        </tr>
        <tr>
          <td><b>成立年期：</b><%= @assessment.shop_age %></td>
          <td><b>業務類型：</b><%= @assessment.shop_type %></td>
        </tr>
        <tr>
          <td colspan="2"><b>主要客源：</b><%= @assessment.client_target %></td>
        </tr>
        <tr>
          <td colspan="2"><b>企業日常營運時使用資訊科技的依賴程度：</b><%= @assessment.it_level %></td>
        </tr>
        <tr>
          <td colspan="2"><b>企業使用社交平台的數量：</b><%= @assessment.social_media_count %></td>
        </tr>
        <tr>
          <td colspan="2"><b>支持聚易用線下支付：</b><%= @assessment.simple_easy_pay %>，<b>線上支付：</b><%= @assessment.online_payment_count %>種</td>
        </tr>
        <tr>
          <td colspan="2"><b>場所地址：</b><%= "#{@assessment.apply.companies_address}" %></td>
        </tr>
    </table>

      <div class="row expanded">
        <h2>診斷分析：</h2>
        <div class="columns medium-7">
            <%= form.text_area :analysis_text, rows: 12 %>

            <%= form.label :last_submit_date, "最後提交日" %>
            <%= form.text_field :last_submit_date %>

            <%= form.label :analysis_date, "診斷報告日期" %>
            <%= form.text_field :analysis_date %>

            <br>
            <%= form.label :report_scan_attachments, "上傳診斷報告scan file" %>
            <% if @assessment.report_scan_attachments.attached? %>
              <%= link_to "診斷報告scan file", url_for(@assessment.report_scan_attachments.last), target: "doc" %>
            <% else %>
              <%= form.file_field :report_scan_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg" %>
            <% end %>

            <br><br>
            <%= form.check_box :is_open_data_to_supplies %>
            <%= form.label :is_open_data_to_supplies, "已發出診斷報告, 開始供應商提交" %>

            <div><br><br>
              <%= form.submit "提交" %>
            </div>
        </div>

        <div class="columns medium-5 suggestions">
          <div id="suggestion-list">
            <% Suggestion.all.each do |suggestion| %>
              <a href="#"><%= suggestion.content %></a>
            <% end %>
          </div>
        </div>
      </div>

    <% end %>

  </div>
</div>


<script>
  $("#suggestion-list").on('click', 'a', function() {
    let pattern = /\d+\、\s*/g;
    var currentListNumber = ($('#assessment_analysis_text').val().match(pattern) || []).length;

    let newSuggestion = $('#assessment_analysis_text').val().replace("\n預期", currentListNumber + 1 + "、" + this.textContent + "\n\n預期");
    $('#assessment_analysis_text').val(newSuggestion);
    return false
  });

</script>