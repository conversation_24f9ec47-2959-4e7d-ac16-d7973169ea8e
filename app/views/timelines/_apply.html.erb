<!-- timeline  -->

<% timelines.each_with_index do |timeline, index| %>

  <% width = "20%" %>
  <% if index % 6 == 0 %>
    <% width = "0%" %>
    <div class="row">
      <div class="medium-10 medium-centered columns">
        <div class="timeline">
  <% end %>

  <div class="active" style="width: <%= width %>;">
    <div class="start">
      <%= link_to edit_apply_timeline_path(timeline.apply, timeline) do %>
        <span><%= timeline.name %><br/><%= timeline.start_at %></span>
      <% end %>
    </div>
  </div>

  <!-- every 6 column or last column -->
  <% if index % 6 == 5 || index == timelines.length - 1 %>
        </div>
      </div>
    </div>
  <% end %>

<% end %>
