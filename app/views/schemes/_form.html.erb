<%= simple_form_for(@scheme) do |f| %>
  <%= f.error_notification %>
  <%= f.error_notification message: f.object.errors[:base].to_sentence if f.object.errors[:base].present? %>

  <div class="form-inputs">
    <%= f.input :name %>
    <%= f.input :case_number_prefix %>
    <%= f.input :enrollment_start_at %>
    <%= f.input :enrollment_end_at %>
    <%= f.input :is_allow_enrollment %>
    <%= f.input :is_open_for_approval %>
  </div>

  <div class="form-actions">
    <%= f.button :submit %>
  </div>
<% end %>
