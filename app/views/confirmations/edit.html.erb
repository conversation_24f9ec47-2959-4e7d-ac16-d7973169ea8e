<%= render 'applies/info' %>


<h1><%= @apply.companies_name %> 確認申請文件</h1>

  <%= form_with id: "confirmation_form", url: apply_confirmation_path, model: @apply.confirmation, local: true do |form| %>

  <table class="unstriped">
        <thead class="thead">
            <tr>
                <th width="20%">收件</th>
                <th width="10%">ECM 確認</th>
                <th width="10%">IST 確認</th>
                <th width="60%">備註</th>
            </tr>
        </thead>
        <tbody>
            <tr>
              <th>申請表</th>
              <td>
                  <label>
                      <%= form.check_box :is_application_pass %>
                      <span>確認無誤</span>
                  </label>
              </td>
              <td></td>
              <td>
                <%= form.rich_text_area :application_remark %>
              </td>
            </tr>
            <tr>
                <th>營業稅（M/1 格式）副本</th>
                <td>
                    <label>
                        <%= form.check_box :is_m1_pass %>
                        <span>確認無誤</span>
                    </label>
                </td>
                <td></td>
                <td>
                  <%= form.rich_text_area :m1_remark %>
                </td>
            </tr>
            <tr>
                <th>個人企業主或股東的<br>身份證明文件副本</th>
                <td>
                    <label>
                        <%= form.check_box :is_id_card_pass %>
                        <span>確認無誤</span>
                    </label>
                </td>
                <td></td>
                <td>
                  <%= form.rich_text_area :id_card_remark %>
                </td>
            </tr>
            <tr>
                <th>店鋪照片</th>
                <td></td>
                <td>
                    <label>
                        <%= form.check_box :is_photos_pass %>
                        <span>確認無誤</span>
                    </label>
                </td>
                <td>
                  <%= form.rich_text_area :photos_remark %>
                </td>
            </tr>
            <tr>
                <th>誠信店證明</th>
                <td>
                    <label>
                        <%= form.check_box :is_certified_shop_pass %>
                        <span>確認無誤</span><br>
                    </label>
                    <label>
                        <%= form.check_box :is_confirmed_certified_shop %>
                        <span>確認為誠信店商戶</span>
                    </label>
                </td>
                <td></td>
                <td>
                  <%= form.rich_text_area :certified_shop_remark %>
                </td>
            </tr>
            <tr>
                <th>其他文件</th>
                <td>
                    <label>
                        <%= form.check_box :is_other_doucments_pass %>
                        <span>確認無誤</span>
                    </label>
                </td>
                <td></td>
                <td>
                  <%= form.rich_text_area :other_document_remark %>
                </td>
            </tr>
      </tbody>
  </table>

  <br><br>

  <table class="review">
      <thead>
          <tr>
            <th colspan="999" class="text-center">企業狀況</th>
          </tr>
      </thead>
      <tbody>

            <tr>
              <td colspan="4">
                <div class="field flex">
                    <p class="text-right"><%= link_to '放棄申請', give_up_apply_path(@apply), method: :patch, class: "button secondary", data: {confirm: '確定商戶放棄?'} %></p>

                    <%= form.check_box :is_first_apply %>
                    <%= form.label :is_first_apply, "首次申請，未申請之前的資助計劃" %>
                </div>
                <br>

                <div class="field flex">
                  <%= form.check_box :is_tax_registered %>
                  <%= form.label :is_tax_registered, "為稅務效力而已在財政局登記營運" %>
                </div>

                <div class="field flex">
                  <%= form.check_box :is_improvable %>
                  <%= form.label :is_improvable, "企業可透過安裝數字化系統進一步優化" %>
                </div>

                <div class="field flex">
                  <%= form.check_box :is_employee_number_not_over %>
                  <%= form.label :is_employee_number_not_over, "企業的總工作人數不超過100人" %>
                </div>

                <div class="field flex">
                  <%= form.check_box :is_operating %>
                  <%= form.label :is_operating, "企業處於適當的營運狀況" %>
                </div>

                <div class="field flex">
                  <%= form.check_box :is_macau_shop %>
                  <%= form.label :is_macau_shop, "企業為一在澳門境內固定地點的實體經營店" %>
                </div>

                <br>

                <div class="field flex">
                  <%= form.check_box :is_confirm_everything %>
                  <%= form.label :is_confirm_everything, "確認所有資料齊全" %>
                </div>

                <br><hr>

                <div class="field flex">
                  <%= form.check_box :is_not_reveal_in_managment_list %>
                  <%= form.label :is_not_reveal_in_managment_list, "資料不全，排除顯示於審閱" %>
                </div>

                <div class="field flex">
                  <%= form.check_box :is_excluded_from_drawing %>
                  <%= form.label :is_excluded_from_drawing, "確認為不合資格參與抽籤" %>
                </div>


              </td>

            </tr>
            <tr>
                <td>
                  <%= form.label :ecm_follower, "ECM跟進人" %>
                  <%= form.text_field :ecm_follower %>
                </td>
                <td>
                  <%= form.label :ist_follower, "IST跟進人" %>
                  <%= form.text_field :ist_follower %>
                </td>
                <td width="25%"></td>
                <td width="25%"></td>
            </tr>

        </tbody>
  </table>

  <p><a onclick="checkAll()" id="check-all-ok">齊全自動 check all</a></p>
  <script>
    function checkAll() {
      document.querySelectorAll('[type="checkbox"]').forEach(checkbox => checkbox.checked = true);
      document.querySelector("#confirmation_is_other_doucments_pass").checked = false;
      document.querySelector("#confirmation_is_not_reveal_in_managment_list").checked = false;
      document.querySelector("#confirmation_is_excluded_from_drawing").checked = false;

      document.querySelector("#confirmation_ist_follower").value = "<%= current_user %>";
    }
  </script>

  <p><%= form.submit "儲存" %></p>
<% end%>

<br><br><br>
<hr>
