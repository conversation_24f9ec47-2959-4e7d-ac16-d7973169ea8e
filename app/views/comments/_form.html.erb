<%= simple_form_for([@apply, comment]) do |form| %>
  <% if comment.errors.any? %>
    <div id="error_explanation">
      <h2><%= pluralize(comment.errors.count, "error") %> prohibited this comment from being saved:</h2>

      <ul>
      <% comment.errors.full_messages.each do |message| %>
        <li><%= message %></li>
      <% end %>
      </ul>
    </div>
  <% end %>

  <%= form.rich_text_area :content %>

  <br>
  <div class="actions">
    <%= form.submit "新增備註" %>
  </div>
<% end %>