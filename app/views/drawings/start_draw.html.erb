<link
  rel="stylesheet"
  href="https://unpkg.com/swiper@8/swiper-bundle.min.css"
/>
<style>
.swiper {
  width: 100%;
  height: 780px;
}
.swiper ol {
    column-count: 2;
    list-style-position: inside;
}
li.catering-registered {
  color: red;
}
</style>
<script src="https://unpkg.com/swiper@8/swiper-bundle.min.js"></script>

<div id="drawing-main">

    <h1>抽籤結果</h1>

    <!-- Slider main container -->
    <div class="swiper">
        <!-- Additional required wrapper -->
        <div class="swiper-wrapper">
            <!-- Slides -->
            <% per_page = 50 %>
            <% page_count = (@applies.count.to_f / per_page).ceil %>
            <% (1..page_count).each do |page| %>
                <div class="swiper-slide">
                    <ol start=<%= (page-1) * per_page + 1 %>>
                    <% @applies[(page-1) * per_page, per_page].each do |apply| %>
                        <%= content_tag :li do %>
                            <%= apply.apply_code %>, <%= apply.companies_tax_number %>, <%= apply.companies_name %>
                        <% end %>
                    <% end %>
                    </ol>
                </div>
            <% end %>
        </div>
        <!-- If we need pagination -->
        <div class="swiper-pagination"></div>

        <!-- If we need navigation buttons -->
        <div class="swiper-button-prev"></div>
        <div class="swiper-button-next"></div>

        <!-- If we need scrollbar -->
        <div class="swiper-scrollbar"></div>
    </div>

</div>


<script>
const swiper = new Swiper('.swiper', {
  // Optional parameters

  // If we need pagination
  pagination: {
    el: '.swiper-pagination',
  },

  // Navigation arrows
  navigation: {
    nextEl: '.swiper-button-next',
    prevEl: '.swiper-button-prev',
  },

  // And if we need scrollbar
  scrollbar: {
    el: '.swiper-scrollbar',
  },

  keyboard: {
    enabled: true,
    onlyInViewport: false,
  },
});
</script>