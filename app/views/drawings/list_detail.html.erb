<style>
.catering-registered {
  color: red;
}
</style>

<h1>詳細抽籤結果</h1>
<p>Seed: <%= @seed %>
<p>* 表示需補交資料商戶</p>

<table>
    <tr>
        <th>序號
        <th>申請編號
        <th>納稅人編號
        <th>場所登記編號
        <th>企業名稱
        <th>情況
        <th>資料齊全/跟進人
        <th>聯絡人
        <th>電話
        <th>職務
        <th>
    </tr>
    <% @applies.each_with_index do |apply, index| %>
        <tr>
            <td>
                <%= index + 1 %>
            <td>
                <%= apply.apply_code %>
            <td>
                <%= apply.owner_tax_number %>
            <td>
                <%= apply.companies_tax_number %>
            <td>
                <p><%= apply.companies_name %></p>
            </td>
            <td>
                <p><small>
                    <% unless apply.confirmation.application_remark.blank? %>
                        申請表: <%= apply.confirmation.application_remark %>
                    <% end %>
                    <% unless apply.confirmation.m1_remark.blank? %>
                        M1: <%= apply.confirmation.m1_remark %>
                    <% end %>
                    <% unless apply.confirmation.id_card_remark.blank? %>
                        身份證: <%= apply.confirmation.id_card_remark %>
                    <% end %>
                    <% unless apply.confirmation.photos_remark.blank? %>
                        相片: <%= apply.confirmation.photos_remark %>
                    <% end %>
                    <% unless apply.confirmation.other_document_remark.blank? %>
                        其他: <%= apply.confirmation.other_document_remark %>
                    <% end %>
                </small></p>
            <td>
                <%= true_tick apply.confirmation.is_confirm_everything %>
                <% unless apply.confirmation.ist_follower.blank? %>
                    <% unless apply.confirmation.is_confirm_everything %>
                        🟠補
                    <% end %>
                <% end %>
                <%= apply.confirmation.ist_follower %>
            <td>
                <%= apply.responsible_officers_name %>
            <td>
                <%= apply.responsible_officers_tel %>
            <td>
                <%= apply.responsible_officers_title %>
            <td>
                <%= link_to "確認申請文件", edit_apply_confirmation_path(apply, apply.confirmation) %>
    <% end %>
</table>