<h2>處理日到期提醒</h2>

<br>
<h4>各阶段在日數內處理</h4>
<ul>
  <% Remind.steps_and_expired_days.each_with_index do |(key, value), index| %>
    <% if index != 0 && index != Remind.steps_and_expired_days.size - 1 %>
      <li><%= "#{t(key)} : #{value} 日" %></li>
    <% end %>
  <% end %>
</ul><br>

<table class="table">
  <thead class="table-dark">
    <tr>
      <th>申請編號</th>
      <th>場所編號</th>
      <th>商戶名稱</th>
      <th width="20%">目前處理阶段</th>
      <th width="12%">上一阶段完成日期</th>
      <th width="8%">此阶段處理日數</th>
      <th width="8%">剩餘處理日數</th>
      <th width="12%">時間表</th>
      <th width="12%"></th>
    </tr>
  </thead>
  <tbody>
    <% @reminds.each do |remind| %>
      <tr>
        <td><%= remind.apply.apply_code %></td>
        <td><%= remind.apply.companies_tax_number %></td>
        <td><%= remind.apply.companies_name %></td>
        <td><%= select_tag :step, options_for_select(remind.get_steps_options) ,{} %></td>
        <td><%= remind.last_step_finished_at %></td>
        <td><%= remind.get_current_step_expired_days() %></td>
        <td><%= remind.get_days_since_last_step() %></td>
        <td><%= link_to "時間表", apply_timelines_path(remind.apply) %></td>
        <td><%= link_to "更改", edit_remind_path(remind) %></td>
      </tr>
    <% end %>
  </tbody>
</table>
