<p><%= t('taxpayer_number') %> <%= @applies.first.owner_tax_number %></p>

<hr>

<ul class="tabs" data-tabs id="apply-tabs">
  <% @applies.each_with_index do |apply, index| %>
    <li class="tabs-title <%= index == 0 ? "is-active" : "" %>">
      <a href="<%= "#panel#{index + 1}" %>">申請企業<%= index + 1 %>
      <% if apply.no_upload_yet? %>
        <div class="alert">【未完成申請】</div>
      <% end %>
      </a>
    </li>
  <% end %>
</ul>

<div class="tabs-content" data-tabs-content="apply-tabs">
  <% @applies.each_with_index do |apply, index| %>
  <div class="tabs-panel <%= index == 0 ? "is-active" : "" %>" id="<%= "panel#{index + 1}" %>">
    <% if apply.is_need_resubmit_attachments %>
      <p class="alert"> (<%= t('required_to_resubmit') %>) <%= t('last_submission_date') %>: <%= apply.last_resubmit_date %> </p>
      <% unless apply.over_last_resubmit_date? %>
        <%= link_to t('late_submittion'), resubmit_apply_path(apply.id), class: "button secondary" %><br/><br/>
      <% end %>
    <% end %>
    <p><%= t('apply_code') %> <%= apply.apply_code %></p>
    <p><%= t('enterprise_name') %> <%= apply.companies_name %></p>
    <p><%= t('venue_registration_number') %> <%= apply.companies_tax_number %></p>

    <br/>
    <% if apply.no_upload_yet? %>
      <p class="alert">【前面的表格內容所生成文檔，經申請人手簽，再上傳，方為完成整個申請流程。】</p>

      <%= link_to "上傳文件", upload_apply_path(apply.id), class: "button" %><br/>
    <% end %>

  </div>
  <% end %>
</div>

<br><br>
