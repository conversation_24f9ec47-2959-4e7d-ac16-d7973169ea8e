<div class="function-area row">
  <div class="columns large-3 function-tabs">
    <ul class="vertical tabs" data-tabs id="example-tabs">
      <li class="tabs-title is-active"><a href="#panel1v" aria-selected="true">資料確認</a></li>

      <% if @apply.stage.equal?("培訓及診斷") || @apply.stage.behind?("培訓及診斷") %>
      <li class="tabs-title"><a href="#panel2v">評估 &amp; 培訓</a></li>
      <% end %>

      <% if @apply.stage.equal?("供應商匹配及提交") || @apply.stage.behind?("供應商匹配及提交") %>
      <li class="tabs-title"><a href="#panel3v">供應商匹配及提交</a></li>
      <% end %>


    </ul>
  </div>
  <div class="columns large-9 function-blocks">
    <div class="tabs-content vertical" data-tabs-content="example-tabs">

      <div class="tabs-panel is-active" id="panel1v">
        <table class="unstriped">
          <tr><td>已確認資料齊全:</td><td width="20%"><%= true_tick_or_cross @apply.confirmation.is_confirm_everything %></td></tr>
          <tr><td>已確認為誠信店商戶:</td><td><%= true_tick_or_cross @apply.confirmation.is_confirmed_certified_shop %></td></tr>
          <tr><td>商戶無須補交申請文件:</td><td><%= true_tick_or_cross !@apply.is_need_resubmit_attachments %></td></tr>
          <tr><td>能進入抽籤阶段:</td><td><%= true_tick_or_cross !@apply.confirmation.is_excluded_from_drawing %></td></tr>
        </table>
        <br>
        <p><%= link_to "申請文件確認", edit_apply_confirmation_path(@apply), class: "button" %></p>
        <p><%= link_to "商戶補交文件", late_apply_path(@apply), class: "button secondary" %></p>
      </div>

      <% if @apply.stage.equal?("培訓及診斷") || @apply.stage.behind?("培訓及診斷") %>
        <div class="tabs-panel" id="panel2v">
          <table class="unstriped">
            <tr><td>已完成培訓:</td><td width="20%"><%= true_tick_or_cross @apply.assessment.is_finished_training %></td></tr>
            <tr><td>已完成診斷:</td><td><%= true_tick_or_cross @apply.assessment.is_finished_assessment %></td></tr>
          </table>
          <br>
          <% if @apply.assessment.report_scan_attachments.attached? %>
            <p><%= link_to "診斷報告scan file", url_for(@apply.assessment.report_scan_attachments.last), target: "doc" %></p>
          <% end %>
          <br>
          <p><%= link_to "修改培訓及診斷", edit_apply_assessment_path(@apply, @apply.assessment), class: "button" %></p>
          <p><%= link_to "診斷報告", report_apply_assessment_path(@apply, @apply.assessment), class: "button secondary" %></p>
          <p><%= link_to "上傳稽查照片", upload_checking_apply_assessment_path(@apply, @apply.assessment), class: "button secondary" %></p>
        </div>
      <% end %>

      <% if @apply.stage.equal?("供應商匹配及提交") || @apply.stage.behind?("供應商匹配及提交") %>
        <div class="tabs-panel" id="panel3v">
          <table class="unstriped">
            <tr><td>已匹配供應商提交:</td><td width="20%"><%= true_tick_or_cross @apply.suppliesApply.present? %></td></tr>
            <tr><td>已發出同意安裝通知書:</td><td><%= true_tick_or_cross @apply.suppliesApply.present? && @apply.suppliesApply.is_sent_install_report %></td></tr>
          </table>
        </div>
      <% end %>

    </div>
  </div>
</div>