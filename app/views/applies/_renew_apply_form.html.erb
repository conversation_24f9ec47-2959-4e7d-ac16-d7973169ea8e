<%= form_with id: "apply_form", url: renew_applies_path, method: :patch, model: @apply, local: true do |form| %>

  <h3><%= t('new_company_application') %></h3>
  <%= render 'error_messages' %>

  <p class="must"><%= t('required_fields') %></p>

  <%= render "apply_form_part1_filled", apply: @apply %>
  <%= render "apply_form_part2", form: form %>
  <%= render "apply_form_part3", form: form %>
  <%= render "apply_form_course_attendee", form: form%>

  <style>
    div.row td input[type=checkbox]  {
      margin: 0;
    }
  </style>

  <div class="row">
    <table>
      <tr>
        <td width="5%" style="text-align: center;"><%= form.check_box :course_agreement, required: true %></td>
        <td>
          <%= form.label :course_agreement, t('course_agreement_statement') %>
        </td>
      </tr>
    </table>

    <table>
      <tr>
        <td width="5%" style="text-align: center;"><%= form.check_box :agreement, required: true %></td>
        <td>
          <%= form.label :agreement, t('agree_statement') %>
        </td>
      </tr>
    </table>
  </div>

  <%= form.submit t('submit'), class: "button" %>

<% end %>

<%= render "upload_javascript" %>
<%= render "renew_confirm_popup_block" %>