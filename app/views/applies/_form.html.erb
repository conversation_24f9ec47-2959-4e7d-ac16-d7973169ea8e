<%= form_with(model: apply) do |form| %>
  <% if apply.errors.any? %>
    <div id="error_explanation">
      <h2><%= pluralize(apply.errors.count, "error") %> prohibited this apply from being saved:</h2>

      <ul>
        <% apply.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="actions">
    <%= form.submit %>
  </div>
<% end %>
