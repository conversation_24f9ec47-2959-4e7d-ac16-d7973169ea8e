<ul class="attachment-list">
  <% attachments.each do |attachment|%>
    <li class="<%= 'opaque' if attachment.visible == false %>">
      <%= link_to url_for(attachment), target: "doc" do %>
        <% if attachment.variable? %>
          <%= image_tag attachment.variant(resize_to_limit: [100, 100]) %><br/>
        <% else %>
          <%= image_tag "/pdf-icon.png", size: "50x50" %><br/>
        <% end %>
      <% end %>
      <span><%= link_to attachment.filename, url_for(attachment), target: "doc" %></span>
      <% if attachment.visible == false %>
      <p>(已隱藏)</p>
      <% else %>
      <p><%= link_to "❌ 隱藏", hide_attachment_apply_path(@apply, upload_id: attachment.id), method: :patch, data: {confirm: 'Are you sure?'} %></p>
      <% end %>
    </li>
  <% end %>
</ul>
