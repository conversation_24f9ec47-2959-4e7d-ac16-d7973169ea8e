<h3><%= @apply.companies_name %> <%= t('submitted_missing_file') %></h3>
<br/>

<% if @apply.position.present? %>
<p><%= t('draw_result_order') %>: <%= @apply.position %></p>
<% end %>
<br>

<div class="must"><%= @apply.resubmit_remark %></div>

<%= form_with id: "apply_form", url: resubmit_applies_path, model: @apply, local: true do |form| %>

<% if @apply.is_need_resubmit_m1 %>
  <div class="row">
      <%= render 'upload_attachments_block',
        title: "<small>3.1.</small> 營業稅 M/1 格式「開業/更改申報表」副本 &nbsp;&nbsp;&nbsp;<a href='https://www.cpttm.org.mo/wp-content/uploads/2022/04/catering-sme-m1-sample.jpg' target='_blank'>範本</a>".html_safe,
        uploadId: "m1_attachments" do %>

        <%= form.file_field :m1_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg", required: true %>
      <% end %>
  </div>
  <br>
<% end %>

<% if @apply.is_need_resubmit_id_card %>
  <div class="row">
      <%= render 'upload_attachments_block',
        title: "<small>3.2.</small> 個人企業主或法定代表人的身份證明文件副本(正面和反面) &nbsp;&nbsp;&nbsp;<a href='https://www.cpttm.org.mo/wp-content/uploads/2022/04/catering-sme-id-sample.jpg' target='_blank'>範本</a>".html_safe,
        uploadId: "id_card_attachments" do %>

        <%= form.file_field :id_card_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg", required: true %>
      <% end %>
  </div>
<% end %>

<% if @apply.is_need_resubmit_photos %>
  <div class="row">
      <%= render 'upload_attachments_block',
        title: "<small>3.3.</small> 最少兩張店鋪照片: 1)含有招牌的門面、2)營業情況".html_safe,
        uploadId: "photos_attachments" do %>

        <%= form.file_field :photos_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg", required: true %>
      <% end %>
  </div>
<% end %>

<% if @apply.is_need_resubmit_other %>
  <div class="row">
    <%= render 'upload_attachments_block',
      title: "<small>3.4.</small> 其他，如法人企業主之商業登記:".html_safe,
      uploadId: "other_attachments", isRequired: false do %>
        <%= form.file_field :other_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg", required: true %>
    <% end %>
  </div>
<% end %>

  <br><br>
  <%= form.submit t('submit'), class: "button" %>

<% end %>


  <script>

    if(isSafariVersionTooOld()) {
      //older safari not support preview
      $(".custom-file-container__custom-file__custom-file-control").hide();
    }

    window.addEventListener("fileUploadWithPreview:imagesAdded", function (e) {

        if(isSafariVersionTooOld()) {
          return;
        }

        // use dataTransfer to set the uploads to input file
        try {
        const dataTransfer = new DataTransfer();
        for(const file of e.detail.cachedFileArray) {
          dataTransfer.items.add(file);
        }
        const fileInput = document.getElementById("apply_" + e.detail.uploadId);
        fileInput.files = dataTransfer.files;
        } catch{}

        refreshUploadPreview(e);
    });

    window.addEventListener("fileUploadWithPreview:imageDeleted", function (e) {

        if(isSafariVersionTooOld()) {
          return;
        }

        // use dataTransfer to set the uploads to input file
        try {
        const dataTransfer = new DataTransfer();
        for(const file of e.detail.cachedFileArray) {
          dataTransfer.items.add(file);
        }
        const fileInput = document.getElementById("apply_" + e.detail.uploadId);
        fileInput.files = dataTransfer.files;
        } catch {}
        refreshUploadPreview(e)
    });


    function refreshUploadPreview(e) {
        if(e.detail.cachedFileArray.length == 0) {
            $("#preview-" + e.detail.uploadId).hide();
        } else {
            $("#preview-" + e.detail.uploadId).show();
        }

        $("#text-" + e.detail.uploadId).html("");
        for(i=0; i<e.detail.cachedFileArray.length; i++) {
            if(e.detail.cachedFileArray[i].size > 20971520) {
                var fileName = $("<p style='color: red;' class='over-size'>" + e.detail.cachedFileArray[i].name + " (" + sizeOf(e.detail.cachedFileArray[i].size)  + ") " + " 上傳檔案大小不能高於 20MB File size cannot exceed 20MB.</p>");
            } else {
                var fileName = $("<p>" + e.detail.cachedFileArray[i].name + " (" + sizeOf(e.detail.cachedFileArray[i].size)  + ") " + "</p>");
            }
            $("#text-" + e.detail.uploadId).append(fileName);
        }
    }

    sizeOf = function (bytes) {
      if (bytes == 0) { return "0.00 B"; }
      var e = Math.floor(Math.log(bytes) / Math.log(1024));
      return (bytes/Math.pow(1024, e)).toFixed(2)+' '+' KMGTP'.charAt(e)+'B';
    }

    function isSafari() {
      return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    }

    function isSafariVersionTooOld() {
      if(!isSafari()) {
        return false;
      }
      var safariVersion = parseInt(/Version\/([\d]+)/.exec(navigator.userAgent)[1]);
      return safariVersion < 11.3;
    }

    $("#apply_form").submit(function(event) {

      if($(this).find(".over-size").length > 0) {
        alert("上傳檔案大小不能高於 20MB. File size cannot exceed 20MB");
        return false;
      }

      var popup = new Foundation.Reveal($('#loadingModal'), {
        closeOnClick: false,
        closeOnEsc:   false
      });
      popup.open();

    });

</script>