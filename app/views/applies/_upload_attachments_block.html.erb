<div class="columns medium-8">

  <div class="custom-file-container" data-upload-id="<%= uploadId %>">
    <label <% unless defined?(isRequired) %>class="must-field"<% end %> ><%= title %>
        <a
            href="javascript:void(0)"
            class="custom-file-container__image-clear"
            title="Clear Image"
        ></a>
    </label>

    <label class="custom-file-container__custom-file">

        <%= yield %>

        <input type="hidden" name="MAX_FILE_SIZE" value="10485760" />
        <span
            class="custom-file-container__custom-file__custom-file-control"
        ></span>
    </label>
    <div id="text-<%= uploadId %>"></div>
    <div id="preview-<%= uploadId %>" class="custom-file-container__image-preview" style="height: 230px; display: none;"></div>
  </div>

  <script>
    var upload = new FileUploadWithPreview("<%= uploadId %>", {
        showDeleteButtonOnImages: true,
        text: {
            chooseFile: "選擇檔案",
            browse: "上傳",
            selectedCount: " 文件選擇",
        },
      });
  </script>

</div>