<script>

    var applyFormUploadSize = 0;
    var idCardUploadSize = 0;
    var m1UploadSize = 0;
    var operatingLicenseUploadSize = 0;
    var businessProofUploadSize = 0;
    var contributionReceiptUploadSize = 0;
    var certifiedShopUploadSize = 0;
    var housingUploadSize = 0;
    var proposalUploadSize = 0;

    if(isSafariVersionTooOld()) {
      //older safari not support preview
      $(".custom-file-container__custom-file__custom-file-control").hide();
    }

    window.addEventListener("fileUploadWithPreview:imagesAdded", function (e) {

        if(isSafariVersionTooOld()) {
          return;
        }

        // use dataTransfer to set the uploads to input file
        try {
        const dataTransfer = new DataTransfer();
        for(const file of e.detail.cachedFileArray) {
          dataTransfer.items.add(file);
        }

        calForSize(e);

        const fileInput = document.getElementById("apply_" + e.detail.uploadId);
        fileInput.files = dataTransfer.files;
        } catch{}

        refreshUploadPreview(e);
    });

    window.addEventListener("fileUploadWithPreview:imageDeleted", function (e) {

        if(isSafariVersionTooOld()) {
          return;
        }

        // use dataTransfer to set the uploads to input file
        try {
        const dataTransfer = new DataTransfer();
        for(const file of e.detail.cachedFileArray) {
          dataTransfer.items.add(file);
        }

        calForSize(e);

        const fileInput = document.getElementById("apply_" + e.detail.uploadId);
        fileInput.files = dataTransfer.files;
        } catch {}
        refreshUploadPreview(e)
    });


    function refreshUploadPreview(e) {
        if(e.detail.cachedFileArray.length == 0) {
            $("#preview-" + e.detail.uploadId).hide();
        } else {
            $("#preview-" + e.detail.uploadId).show();
        }

        $("#text-" + e.detail.uploadId).html("");
        for(i=0; i<e.detail.cachedFileArray.length; i++) {
            if(e.detail.cachedFileArray[i].size > 12582912) {
                var fileName = $("<p style='color: red;' class='over-size'>" + e.detail.cachedFileArray[i].name + " (" + sizeOf(e.detail.cachedFileArray[i].size)  + ") " + " 上傳檔案大小不能高於 20MB File size cannot exceed 20MB.</p>");
            } else {
                var fileName = $("<p>" + e.detail.cachedFileArray[i].name + " (" + sizeOf(e.detail.cachedFileArray[i].size)  + ") " + "</p>");
            }
            $("#text-" + e.detail.uploadId).append(fileName);
        }
    }

    sizeOf = function (bytes) {
      if (bytes == 0) { return "0.00 B"; }
      var e = Math.floor(Math.log(bytes) / Math.log(1024));
      return (bytes/Math.pow(1024, e)).toFixed(2)+' '+' KMGTP'.charAt(e)+'B';
    }

    function isSafari() {
      return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    }

    function isSafariVersionTooOld() {
      if(!isSafari()) {
        return false;
      }
      var safariVersion = parseInt(/Version\/([\d]+)/.exec(navigator.userAgent)[1]);
      return safariVersion < 11.3;
    }

    function calForSize(e) {
      var size = 0;
      for(i=0; i<e.detail.cachedFileArray.length; i++) {
        size += e.detail.cachedFileArray[i].size;
      }

      if ('apply_form_attachments' == e.detail.uploadId) {
        applyFormUploadSize = size;
      } else if ('id_card_attachments' == e.detail.uploadId) {
        idCardUploadSize = size;
      } else if ('m1_attachments' == e.detail.uploadId) {
        m1UploadSize = size;
      } else if ('business_proof_attachments' == e.detail.uploadId) {
        businessProofUploadSize = size;
      } else if ('operating_license_attachments' == e.detail.uploadId) {
        operatingLicenseUploadSize = size;
      } else if ('contributionReceiptUploadSize' == e.detail.uploadId) {
        contributionReceiptUploadSize = size;
      } else if ('certified_shop_attachments' == e.detail.uploadId) {
        certifiedShopUploadSize = size;
      } else if('housing_attachments' == e.detail.uploadId) {
        housingUploadSize = size;
      } else if('proposal_attachments' == e.detail.uploadId) {
        proposalUploadSize = size;
      }
    }

    $("#upload_form").submit(function(event) {

      if($(this).find(".over-size").length > 0) {
        alert("上傳檔案大小不能高於 12MB");
        return false;
      }

      if((applyFormUploadSize + idCardUploadSize + m1UploadSize + businessProofUploadSize + operatingLicenseUploadSize + contributionReceiptUploadSize + certifiedShopUploadSize + housingUploadSize + proposalUploadSize) > 52428800 ) {
        alert("上傳檔案總大小不能高於 50MB");
        return false;
      }

    });

</script>