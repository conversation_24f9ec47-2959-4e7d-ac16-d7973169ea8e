<ul class="attachment-list">
  <% attachments.where(visible: [nil, true]).each do |attachment|%>
   <li>
    <%= link_to url_for(attachment), target: "doc" do %>
      <% if attachment.variable? %>
        <%= image_tag attachment.variant(resize_to_limit: [100, 100]) %><br/>
      <% else %>
        <%= image_tag "/pdf-icon.png", size: "50x50" %><br/>
      <% end %>
    <% end %>
    <span><%= link_to attachment.filename, url_for(attachment), target: "doc" %></span>
  </li>
  <% end %>
</ul>
