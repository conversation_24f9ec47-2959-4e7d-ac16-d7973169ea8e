<h3><%= @apply.companies_name %> <%= t('submitted_missing_file') %></h3>
<%= render 'error_messages' %>
<br/>

<div class="must"><%= t('information_submission') %>: <%= @apply.resubmit_remark %></div>

<%= form_with id: "apply_form", url: resubmit_update_apply_path, model: @apply, local: true do |form| %>

<% if @apply.is_need_resubmit_m1 %>
  <div class="row">
      <%= render 'upload_attachments_block',
        title: "<small>3.1.</small> ".html_safe + t('copy_of_business_tax_in_m1_format') + " <strong style='font-size: 1.2em;'>".html_safe + t('front_side') + "</strong>".html_safe + "&nbsp;&nbsp;&nbsp;<a href='https://www.cpttm.org.mo/wp-content/uploads/2022/04/catering-sme-m1-sample.jpg' target='_blank'>".html_safe + t('sample') + "</a>".html_safe,
        uploadId: "m1_attachments" do %>

        <%= form.file_field :m1_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg", required: true %>
      <% end %>
  </div>
  <br>
<% end %>

<% if @apply.is_need_resubmit_m1_backside %>
  <div class="row">
      <%= render 'upload_attachments_block',
        title: "<small>3.2.</small> ".html_safe + t('copy_of_business_tax_in_m1_format') + " <strong style='font-size: 1.2em;'>".html_safe + t('back_side') + "</strong>".html_safe + "&nbsp;&nbsp;&nbsp;<a href='https://www.cpttm.org.mo/wp-content/uploads/2022/04/catering-sme-m1-sample.jpg' target='_blank'>".html_safe + t('sample') + "</a>".html_safe,
        uploadId: "m1_backside_attachments" do %>

        <%= form.file_field :m1_backside_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg", required: true %>
      <% end %>
  </div>
  <br>
<% end %>

<% if @apply.is_need_resubmit_id_card %>
  <div class="row">
      <%= render 'upload_attachments_block',
        title: "<small>3.3.</small> ".html_safe + t('identity_document_of_the_individual_business_owner_or_legal') + " &nbsp;&nbsp;&nbsp;<a href='https://www.cpttm.org.mo/wp-content/uploads/2022/04/catering-sme-id-sample.jpg' target='_blank'>".html_safe + t("sample") + "</a>".html_safe,
        uploadId: "id_card_attachments" do %>

        <%= form.file_field :id_card_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg", required: true %>
      <% end %>
  </div>
  <br>
<% end %>

<% if @apply.is_need_resubmit_photos %>
  <div class="row">
      <%= render 'upload_attachments_block',
        title: "<small>3.4.</small> ".html_safe + t('two_photos') + "<br/>".html_safe + t('two_photos_first') + "<br/>".html_safe + t('two_photos_second'),
        uploadId: "photos_attachments" do %>

        <%= form.file_field :photos_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg", required: true %>
      <% end %>
  </div>
  <br>
<% end %>

<% if @apply.is_need_resubmit_certified_shop %>
  <div class="row">
      <%= render 'upload_attachments_block',
        title: "<small>3.5.</small> ".html_safe + t('certified_shop_documents') + "<br/>".html_safe,
        uploadId: "certified_shop_attachments" do %>

        <%= form.file_field :certified_shop_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg", required: true %>
      <% end %>
  </div>
  <a href='https://www.consumer.gov.mo/csenq?usrkey=ipFcr7Vk8A20ghjxXNEM82zQ_' target='_blank'><%= t('inquiry_about_certified_shop') %></a>
  <br><br>
<% end %>

<% if @apply.is_need_resubmit_other %>
  <div class="row">
    <%= render 'upload_attachments_block',
      title: "<small>3.6.</small> ".html_safe + t('others_business_registration_documents'),
      uploadId: "other_attachments", isRequired: false do %>
        <%= form.file_field :other_attachments, multiple: true, accept: "application/pdf,image/png,image/jpeg", required: false %>
    <% end %>
  </div>
  <br>
<% end %>

  <br>
  <%= form.submit t('submit'), class: "button" %>

<% end %>

<%= render "upload_javascript" %>