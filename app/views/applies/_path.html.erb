<style>
ul.path {
  list-style: none;
  display: flex; /* 使用 Flexbox 佈局 */
  justify-content: flex-start; /* 預設靠左 */
  flex-wrap: nowrap; /* 防止換行 */
  align-items: center;
  margin: 0;
  padding: 0;
  gap: 10px;
}

ul.path li {
  text-align: center;
  position: relative;
  align-items: center;
}

ul.path li::after {
  content: "→";
  position: absolute;
  right: -10px;
  top: 50%;
  transform: translateY(-50%);
}

ul.path li.last {
  color: #cecece;
}

ul.path li.last::after, li.give-up::after {
  display: none;
}


</style>


<ul class="path">
  <% @apply.stage.getPath.each do |stage|  %>
    <li class="columns large-2"><%= stage %></li>
  <% end %>
  <% if @apply.give_up? %>
    <li class="columns large-2 give-up">(已放棄)</li>
  <% elsif !@apply.stage.completed? %>
    <li class="columns large-2 last">(下一階段):<br/><%= @apply.stage.next %></li>
  <% end %>
</ul>
