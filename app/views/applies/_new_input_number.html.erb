<h4><%= t('please_input_numbers_to_next_steps') %></h4>
<br><br>
<p><%= t('registered_before_statement') %></p>

<%= form_with url: start_applies_path, local: true do |form| %>

  <%= form.hidden_field :next_step, value: "forms" %>

  <br><br>
  <div class="row">
    <div class="columns large-8">
      <%= form.label :owner_tax_number, t('owner_and_taxpayer_number'), class: "must-field" %>
      <%= form.text_field :owner_tax_number, required: true, maxlength: 12 %>
    </div>
  </div>

  <div class="row">
    <div class="columns large-8">
      <%= form.label :companies_tax_number, t('apply_venue_registration_number'), class: "must-field" %>
      <%= form.text_field :companies_tax_number, required: true, maxlength: 6 %>
    </div>
  </div>

  <br>
  <%= form.submit t('next_step'), class: "button radius" %>
  <%= link_to t('return'), applies_path, class: "button secondary back" %>

<% end %>
