<h3><%= t('query_apply_and_resubmit') %></h3>
<h5><%= t('verify_your_taxpayer_number') %></h5>
<br>

<%= form_with local: true do |form| %>

  <%= form.hidden_field :next_step, value: "check_m1_and_captcha" %>

  <div class="row">
    <div class="columns medium-8">
      <%= form.label :owner_tax_number, t('taxpayer_number') %>
      <%= form.text_field :owner_tax_number, required: true %>
    </div>
  </div>

  <div class="row">
    <div class="columns medium-4">
      <%= form.label :input_verification_code, t('input_verification_code') %>
      <%= rucaptcha_input_tag(class: 'form-control', placeholder: t('input_verification_code'), required: true) %>
    </div>
    <%= rucaptcha_image_tag(alt: 'Captcha') %>
  </div>

  <br>
  <%= form.submit t('query'), class: "button" %>
  <%= link_to t('return'), applies_path, class: "button secondary back" %>

<% end %>