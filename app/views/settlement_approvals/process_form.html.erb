<div class="container process_form">

  <div class="row">
    <h4 class="text-center">中小企業後台數字化支援服務計劃 結算資料表</h4>
    <div style="overflow: hidden;">
      <table style=' float: right;'>
        <tr>
          <td width="50%">申請編號</td>
          <td>
            <strong><%= @apply.apply_code %></strong>
          </td>
        </tr>
      </table>
    </div>

    <table>
        <tr>
          <td>企業主名稱</td>
          <td colspan=3>
              <%= @apply.owner_name %>
          </td>
        </tr>
        <tr>
          <td>納稅人編號</td>
          <td colspan=3>
              <%= @apply.owner_tax_number %>
          </td>
        </tr>
        <tr>
          <td>商號名稱</td>
          <td colspan=3>
              <%= @apply.companies_name %>
          </td>
        </tr>
        <tr>
          <td>場所登記編號</td>
          <td colspan=3>
              <%= @apply.companies_tax_number %>
          </td>
        </tr>
        <tr>
          <td>場所地址</td>
          <td colspan=5>
              <%= @apply.companies_address %>
          </td>
        </tr>
      </table>

      <table class="review">
        <tr>
          <th>結算工作報告</th>
          <th>填寫人</th>
        </tr>
        <tr>
          <td rowspan=3 style='width: 85%;'>
            <%= simple_format @apply.suppliesApply.settlement_report %>

            <% if @current_user.is_thomas? && @apply.suppliesApply.settlement_report.present? %>
              <%= simple_form_for @settlement_approval, url: "/settlement_approvals/#{@settlement_approval.id}/handle" do |form| %>
                <%= form.text_area :comment_by_manager %>
                <%= form.button :submit, "同意資格", class: "approval-button button", data:{confirm:"確認為同意(Approve)？"} %>
                <%= form.button :submit, "不同意資格", class: "disapproval-button button", data:{confirm:"確認為不同意(Disapprove)？"} %>
                <%= form.button :submit, "儲存，但未上呈", class: "button" %>
              <% end %>
            <% else %>
              <div style="font-weight: bold;font-size:1.2em;margin:1em 0;color:darkblue"><%= @settlement_approval.comment_by_manager %></div>
            <% end %>
          </td>
          <td>Mak Seng Hin</td>
        </tr>
        <tr>
          <td>簽署及日期</td>
        </tr>
        <tr>
          <td>
            <% if @settlement_approval.approved_by_manager == true %>
              <span class="signature">
                Approved
              </span>
              <br><small>by Department Manager</small>
              <br><small><%= @settlement_approval.approved_by_manager_at.to_date %></small>

              <% elsif @settlement_approval.approved_by_manager == false %>

              <span class="disapproved">Disapproved</span>
              <br><small><%= @settlement_approval.approved_by_manager_at.to_date %></small>
            <% end %>
          </td>
        </tr>

        <tr>
          <th>部門主管意見</th>
          <th width="15%">簽署及日期</th>
        </tr>
        <tr>
          <td>
              <% if @current_user.is_sm? %>
                <%= simple_form_for @settlement_approval, url: "/settlement_approvals/#{@settlement_approval.id}/handle" do |form| %>
                  <%= form.text_area :comment_by_sm %>
                  <%= form.button :submit, "同意資格", class: "approval-button button", data:{confirm:"確認為同意(Approve)？"} %>
                  <%= form.button :submit, "不同意資格", class: "disapproval-button button", data:{confirm:"確認為不同意(Disapprove)？"} %>
                <% end %>
              <% else %>
                <%= simple_format @settlement_approval.comment_by_sm %>
              <% end %>
          </td>
          <td>
            <% if @settlement_approval.approved_by_sm == true %>
              <span class="signature">
                Approved
              </span>
              <br><small>by Senior Manager</small>
              <br><small><%= @settlement_approval.approved_by_sm_at.to_date %></small>
            <% elsif @settlement_approval.approved_by_sm == false %>
              <span class="disapproved">
                Disapproved
              </span>
              <br><small>by Senior Manager</small>
              <br><small><%= @settlement_approval.approved_by_sm_at.to_date %></small>
            <% end %>
          </td>
        </tr>
      </table>

      <table class="review">
        <tr>
          <th>財務部覆核</th>
          <th width="15%">簽署及日期</th>
        </tr>

        <tr>
          <td>
              <% if @current_user.is_fin? %>
                <%= simple_form_for @settlement_approval, url: "/settlement_approvals/#{@settlement_approval.id}/handle" do |form| %>
                  <%= form.text_area :comment_by_fin %>
                  <%= form.button :submit, "同意結算", class: "approval-button button", data:{confirm:"確認為同意(Approve)？"} %>
                  <%= form.button :submit, "不同意結算", class: "disapproval-button button", data:{confirm:"確認為不同意(Disapprove)？"} %>
                <% end %>
              <% else %>
                <%= simple_format @settlement_approval.comment_by_fin %>
              <% end %>
          </td>
          <td>
            <% if @settlement_approval.verified_by_fin == true %>
              <span class="signature">
                Approved
              </span>
              <br><small>by FIN</small>
              <br><small><%= @settlement_approval.verified_by_fin_at.to_date %></small>
            <% elsif @settlement_approval.verified_by_fin == false %>
              <span class="disapproved">
                Disapproved
              </span>
              <br><small>by FIN</small>
              <br><small><%= @settlement_approval.verified_by_fin_at.to_date %></small>
            <% end %>
          </td>
        </tr>

      </table>


      <table class="review">
        <tr>
          <th colspan=2>副理事長意見</th>
          <th colspan=2>理事長意見</th>
        </tr>
        <tr>
          <td colspan=2 style='width: 50%;'>
            <% if @current_user.is_ddg? %>
              <%= simple_form_for @settlement_approval, url: "/settlement_approvals/#{@settlement_approval.id}/handle" do |form| %>
                <%= form.text_area :comment_by_ddg %>
                <%= form.button :submit, "同意資格", class: "approval-button button", data:{confirm:"確認為同意(Approve)？"} %>
                <%= form.button :submit, "不同意資格", class: "disapproval-button button", data:{confirm:"確認為不同意(Disapprove)？"} %>
              <% end %>
            <% else %>
              <%= simple_format @settlement_approval.comment_by_ddg %>
            <% end %>

          </td>
          <td colspan=2>
            <% if current_user.is_dg? %>
              <%= simple_form_for @settlement_approval, url: "/settlement_approvals/#{@settlement_approval.id}/handle" do |form| %>
                <%= form.text_area :comment_by_dg %>
                <%= form.button :submit, "同意資格", class: "approval-button button", data:{confirm:"確認為同意(Approve)？"} %>
                <%= form.button :submit, "不同意資格", class: "disapproval-button button", data:{confirm:"確認為不同意(Disapprove)？"} %>
              <% end %>
            <% else %>
              <%= simple_format @settlement_approval.comment_by_dg %>
            <% end %>

          </td>
        </tr>
        <tr>
          <td style='width: 15%'>簽署及日期</td>
          <td>
            <% if @settlement_approval.approved_by_ddg == true %>
              <span class="signature">
                Approved
              </span>
              <br><small>by DDG</small>
              <br><small><%= @settlement_approval.approved_by_ddg_at.to_date %></small>
            <% elsif @settlement_approval.approved_by_ddg == false %>
              <span class="disapproved">
                Disapproved
              </span>
              <br><small>by DDG</small>
              <br><small><%= @settlement_approval.approved_by_ddg_at.to_date %></small>
            <% end %>
          </td>
          <td style='width: 15%'>簽署及日期</td>
          <td>
            <% if @settlement_approval.approved_by_dg == true %>
              <span class="signature">
                Approved
              </span>
              <br><small>by DG</small>
              <br><small><%= @settlement_approval.approved_by_dg_at.to_date %></small>
            <% elsif @settlement_approval.approved_by_dg == false %>
              <span class="disapproved">
                Disapproved
              </span>
              <br><small>by DG</small>
              <br><small><%= @settlement_approval.approved_by_dg_at.to_date %></small>
            <% end %>
          </td>
        </tr>

      </table>
      <hr>
      <h2><center>詳情</center></h2>

      <div class="row expanded">
        <div class="columns">
          <table class="review">
            <tr>
              <th>序</th>
              <th>文件</th>
              <th>備註</th>
            </tr>
            <tr>
              <td>1</td>
              <td>營業稅 M/1 格式「開業/更改申報表」副本</td>
              <td>
                <p>(數目: <%= @apply.m1_attachments.count %>)</p>
                <%= render 'applies/detail_attachments', attachments: @apply.m1_attachments %>
              </td>
            </tr>
            <tr>
              <td>2</td>
              <td>上傳個人企業主或法定代表人的身份證明文件副本</td>
              <td>
                <p>(數目: <%= @apply.id_card_attachments.count %>)</p>
                <%= render 'applies/detail_attachments', attachments: @apply.id_card_attachments %>
              </td>
            </tr>
          </table>
        </div>
      </div>

      <div class="row expanded">
        <div class="columns">
          <h2>供應商上傳檔案</h2>
          <table class="review">
            <tr>
              <th></th>
              <th>文件</th>
              <th>備註</th>
            </tr>
            <tr>
              <td>1</td>
              <td>報價單</td>
              <td>
                <p>(數目: <%= @apply.suppliesApply.quotation_attachments.count %>)</p>
                <%= render 'applies/detail_attachments', attachments: @apply.suppliesApply.quotation_attachments %>
              </td>
            </tr>
            <tr>
              <td>2</td>
              <td>商戶授權書</td>
              <td>
                <p>商戶授權書 (數目: <%= @apply.suppliesApply.authorization_letter_attachments.count %>)</p>
                <%= render 'applies/detail_attachments', attachments: @apply.suppliesApply.authorization_letter_attachments %>

              </td>
            </tr>
          </table>
        </div>


          <%= render 'supplies_applies/settlement_detail' %>


        <div class="columns">
          <%= render 'supplies_applies/checking_photos_detail', supplies_apply: @apply.suppliesApply %>
        </div>

        <table class='noborder'>
          <tr>
            <td>該單位，屬</td>
            <td>
                <%= check_box_tag :cfg, 1, @apply.is_registered_catering %><label>已申請餐飲資助計劃</label>
            </td>
            <td>
                <%= check_box_tag :cfg, 1, !@apply.is_registered_catering %><label>未申請餐飲資助計劃</label>
            </td>
          </tr>
        </table>

        <div style="margin: 5em 0; " class='text-center'>
          預計對本申請的資助金額為澳門元
          <u>
            <%= number_to_currency @apply.suppliesApply.amounts %>
          </u> 圓。
        </div>

        <hr>

  </div>



</div>