# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2025_09_30_071455) do

  create_table "action_text_rich_texts", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.text "body", size: :long
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_storage_attachments", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.boolean "visible"
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum", null: false
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "all_applies_records", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "companies_tax_number"
    t.string "owner_tax_number"
    t.string "project_name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "applies", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "owner_name"
    t.string "owner_tax_number"
    t.string "owner_so_number"
    t.boolean "owner_is_personal"
    t.boolean "owner_is_legal"
    t.boolean "owner_is_others"
    t.string "owner_others_description"
    t.string "responsible_officers_name"
    t.string "responsible_officers_tel"
    t.string "responsible_officers_title"
    t.string "responsible_officers_email"
    t.string "responsible_officers_area"
    t.string "responsible_officers_address"
    t.string "companies_name"
    t.string "companies_tax_number"
    t.string "companies_staff_number"
    t.string "companies_area"
    t.string "companies_address"
    t.string "apply_code"
    t.bigint "scheme_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "current_confirm_status", default: "registered", null: false
    t.boolean "is_need_resubmit_m1", default: false
    t.boolean "is_need_resubmit_id_card", default: false
    t.boolean "is_need_resubmit_photos", default: false
    t.boolean "is_need_resubmit_other", default: false
    t.text "resubmit_remark"
    t.string "valid_code", limit: 6
    t.datetime "valid_code_sent_at"
    t.string "current_audit_status", default: "audit_waiting"
    t.string "drawing_status"
    t.integer "drawing_position"
    t.boolean "is_new_resubmit", default: false
    t.string "prefer_supplies"
    t.boolean "is_need_resubmit_m1_backside"
    t.boolean "is_need_resubmit_certified_shop"
    t.boolean "is_need_resubmit_attachments"
    t.string "current_stage"
    t.boolean "is_gave_up"
    t.boolean "is_soft_deleted"
    t.date "last_resubmit_date"
    t.boolean "is_industry_catering"
    t.boolean "is_industry_retail"
    t.string "companies_macau_staff_number"
    t.string "shop_housing_number"
    t.boolean "is_apply_type_1"
    t.boolean "is_apply_type_2"
    t.boolean "is_agree_data_collect"
    t.index ["scheme_id"], name: "index_applies_on_scheme_id"
  end

  create_table "approvals", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.boolean "approved_by_manager"
    t.boolean "approved_by_sm"
    t.boolean "approved_by_ddg"
    t.boolean "approved_by_dg"
    t.datetime "approved_by_manager_at"
    t.datetime "approved_by_sm_at"
    t.datetime "approved_by_ddg_at"
    t.datetime "approved_by_dg_at"
    t.text "comment_by_manager"
    t.text "comment_by_sm"
    t.text "comment_by_ddg"
    t.text "comment_by_dg"
    t.bigint "apply_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["apply_id"], name: "index_approvals_on_apply_id"
  end

  create_table "assessments", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.date "assessment_date"
    t.boolean "is_finished_assessment", default: false
    t.text "assessment_remark"
    t.date "training_date"
    t.boolean "is_finished_training", default: false
    t.text "training_remark"
    t.bigint "apply_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "shop_age"
    t.string "shop_type"
    t.string "client_target"
    t.string "it_level"
    t.string "social_media_count"
    t.string "simple_easy_pay"
    t.string "online_payment_count"
    t.string "external_environment_difficult"
    t.string "operating_difficult"
    t.string "company_strategy"
    t.string "full_time_employee"
    t.string "part_time_employee"
    t.string "employee_avg_age"
    t.string "business_expectations"
    t.text "analysis_text"
    t.boolean "is_open_data_to_supplies", default: false
    t.string "catering_supplies_name"
    t.string "catering_supplies_tel"
    t.boolean "is_absent_training", default: false
    t.string "analysis_date"
    t.boolean "is_submit_quotation", default: false
    t.string "it_support"
    t.string "business_diff"
    t.string "last_submit_date"
    t.index ["apply_id"], name: "index_assessments_on_apply_id"
  end

  create_table "comments", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "content"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "apply_id"
    t.bigint "user_id"
    t.index ["apply_id"], name: "index_comments_on_apply_id"
    t.index ["user_id"], name: "index_comments_on_user_id"
  end

  create_table "confirmations", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.boolean "is_application_pass", default: false
    t.boolean "is_m1_pass", default: false
    t.boolean "is_id_card_pass", default: false
    t.boolean "is_photos_pass", default: false
    t.boolean "is_other_doucments_pass", default: false
    t.text "application_remark"
    t.text "m1_remark"
    t.text "id_card_remark"
    t.text "photos_remark"
    t.text "other_document_remark"
    t.boolean "is_first_apply", default: false
    t.boolean "is_tax_registered", default: false
    t.boolean "is_improvable", default: false
    t.boolean "is_employee_number_not_over", default: false
    t.boolean "is_operating", default: false
    t.boolean "is_macau_shop", default: false
    t.boolean "is_confirm_everything", default: false
    t.string "ist_follower"
    t.string "ecm_follower"
    t.bigint "apply_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "is_not_reveal_in_managment_list", default: false
    t.string "restaurant_category"
    t.boolean "is_certified_shop_pass"
    t.string "certified_shop_remark"
    t.boolean "is_confirmed_certified_shop"
    t.boolean "is_excluded_from_drawing"
    t.index ["apply_id"], name: "index_confirmations_on_apply_id"
  end

  create_table "drawing_histories", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "seed"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "reminds", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "current_step"
    t.date "last_step_finished_at"
    t.bigint "apply_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["apply_id"], name: "index_reminds_on_apply_id"
  end

  create_table "schemes", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "case_number_prefix"
    t.date "enrollment_start_at"
    t.date "enrollment_end_at"
    t.boolean "is_allow_enrollment", default: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "is_open_for_approval"
  end

  create_table "settlement_approvals", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.boolean "approved_by_manager"
    t.boolean "approved_by_sm"
    t.boolean "approved_by_ddg"
    t.boolean "approved_by_dg"
    t.datetime "approved_by_manager_at"
    t.datetime "approved_by_sm_at"
    t.datetime "approved_by_ddg_at"
    t.datetime "approved_by_dg_at"
    t.text "comment_by_manager"
    t.text "comment_by_sm"
    t.text "comment_by_ddg"
    t.text "comment_by_dg"
    t.bigint "apply_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "verified_by_fin"
    t.datetime "verified_by_fin_at"
    t.text "comment_by_fin"
    t.index ["apply_id"], name: "index_settlement_approvals_on_apply_id"
  end

  create_table "sms_records", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "apply_id"
    t.string "mobile"
    t.date "date"
    t.string "content"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "suggestions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "content"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "supplies_applies", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "apply_code"
    t.string "companies_name"
    t.string "companies_tax_number"
    t.text "description"
    t.bigint "user_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "apply_id"
    t.boolean "is_accept_apply", default: false
    t.string "amounts"
    t.text "report"
    t.string "install_report_address"
    t.string "install_report_person_name"
    t.string "install_report_date"
    t.boolean "is_sent_install_report"
    t.text "settlement_report"
    t.boolean "is_ready_settlement_approval"
    t.string "install_report_serial_number"
    t.boolean "is_finish_settlement"
    t.bigint "scheme_id", default: 1
    t.index ["apply_id"], name: "index_supplies_applies_on_apply_id"
    t.index ["scheme_id"], name: "index_supplies_applies_on_scheme_id"
    t.index ["user_id"], name: "index_supplies_applies_on_user_id"
  end

  create_table "timelines", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.date "start_at"
    t.bigint "apply_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["apply_id"], name: "index_timelines_on_apply_id"
  end

  create_table "uploads", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "apply_id"
    t.index ["apply_id"], name: "index_uploads_on_apply_id"
  end

  create_table "user_assessments", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "assessment_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["assessment_id"], name: "index_user_assessments_on_assessment_id"
    t.index ["user_id"], name: "index_user_assessments_on_user_id"
  end

  create_table "users", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.integer "failed_attempts", default: 0, null: false
    t.datetime "locked_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "is_supplies", default: false
    t.string "supplies_name"
    t.index ["email"], name: "index_users_on_email", unique: true
  end

  create_table "valid_code_records", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "apply_id"
    t.date "date"
    t.integer "try_count"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "applies", "schemes"
  add_foreign_key "approvals", "applies"
  add_foreign_key "assessments", "applies"
  add_foreign_key "comments", "applies"
  add_foreign_key "comments", "users"
  add_foreign_key "confirmations", "applies"
  add_foreign_key "reminds", "applies"
  add_foreign_key "settlement_approvals", "applies"
  add_foreign_key "supplies_applies", "applies"
  add_foreign_key "supplies_applies", "schemes"
  add_foreign_key "supplies_applies", "users"
  add_foreign_key "timelines", "applies"
  add_foreign_key "uploads", "applies"
  add_foreign_key "user_assessments", "users"
end
