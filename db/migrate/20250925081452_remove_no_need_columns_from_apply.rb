class RemoveNoNeedColumnsFromApply < ActiveRecord::Migration[6.1]
  def change
    remove_columns :applies, :courses_period_1, :courses_period_2, :courses_period_3, :courses_period_4, :courses_period_5, :courses_period_6, :courses_period_7, :certified_shop_status, :attendee_name_1, :attendee_name_2, :attendee_title_1, :attendee_title_2, :is_registered_catering, :industry, :contact_person_name, :contact_person_tel, :contact_person_title, :contact_person_email
  end
end
