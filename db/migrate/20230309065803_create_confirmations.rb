class CreateConfirmations < ActiveRecord::Migration[6.1]
  def change
    create_table :confirmations do |t|

      t.boolean   :is_application_pass, default: false
      t.boolean   :is_m1_pass, default: false
      t.boolean   :is_id_card_pass, default: false
      t.boolean   :is_photos_pass, default: false
      t.boolean   :is_other_doucments_pass, default: false
      t.text      :application_remark
      t.text      :m1_remark
      t.text      :id_card_remark
      t.text      :photos_remark
      t.text      :other_document_remark
      t.boolean   :is_first_apply, default: false
      t.boolean   :is_tax_registered, default: false
      t.boolean   :is_improvable, default: false
      t.boolean   :is_employee_number_not_over, default: false
      t.boolean   :is_operating, default: false
      t.boolean   :is_macau_shop, default: false
      t.boolean   :is_confirm_everything, default: false
      t.string    :ist_follower
      t.string    :ecm_follower
      t.references :apply, null: false, foreign_key: true

      t.timestamps
    end
  end
end
