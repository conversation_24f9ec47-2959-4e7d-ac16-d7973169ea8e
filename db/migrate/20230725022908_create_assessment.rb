class CreateAssessment < ActiveRecord::Migration[6.1]
  def change
    create_table :assessments do |t|

      t.date :assessment_date
      t.boolean :is_finished_assessment, default: false
      t.text :assessment_remark
      t.date :training_date
      t.boolean :is_finished_training, default: false
      t.text :training_remark

      t.references :apply, null: true, foreign_key: true

      t.timestamps
    end
  end
end
