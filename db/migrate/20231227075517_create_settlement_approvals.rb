class CreateSettlementApprovals < ActiveRecord::Migration[6.1]
  def change
    create_table :settlement_approvals do |t|
      t.boolean "approved_by_manager"
      t.boolean "approved_by_sm"
      t.boolean "approved_by_ddg"
      t.boolean "approved_by_dg"
      t.datetime "approved_by_manager_at"
      t.datetime "approved_by_sm_at"
      t.datetime "approved_by_ddg_at"
      t.datetime "approved_by_dg_at"
      t.text "comment_by_manager"
      t.text "comment_by_sm"
      t.text "comment_by_ddg"
      t.text "comment_by_dg"

      t.references :apply, null: true, foreign_key: true
      t.timestamps
    end
  end
end
