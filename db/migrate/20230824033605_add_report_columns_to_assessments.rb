class AddReportColumnsToAssessments < ActiveRecord::Migration[6.1]
  def change
    add_column :assessments, :shop_age, :string
    add_column :assessments, :shop_type, :string
    add_column :assessments, :client_target, :string
    add_column :assessments, :it_level, :string
    add_column :assessments, :social_media_count, :string
    add_column :assessments, :simple_easy_pay, :string
    add_column :assessments, :online_payment_count, :string
    add_column :assessments, :external_environment_difficult, :string
    add_column :assessments, :operating_difficult, :string
    add_column :assessments, :company_strategy, :string
    add_column :assessments, :full_time_employee, :string
    add_column :assessments, :part_time_employee, :string
    add_column :assessments, :employee_avg_age, :string
    add_column :assessments, :business_expectations, :string

    add_column :assessments, :analysis_text, :text
    add_column :assessments, :is_open_data_to_supplies, :boolean, default: false
  end
end
