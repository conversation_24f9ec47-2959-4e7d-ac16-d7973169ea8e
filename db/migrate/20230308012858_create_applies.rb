class CreateApplies < ActiveRecord::Migration[6.1]
  def change
    create_table :applies do |t|

      t.string    :owner_name
      t.string    :owner_tax_number
      t.string    :owner_so_number
      t.boolean   :owner_is_personal
      t.boolean   :owner_is_legal
      t.boolean   :owner_is_others
      t.string    :owner_others_description
      t.string    :responsible_officers_name
      t.string    :responsible_officers_tel
      t.string    :responsible_officers_title
      t.string    :responsible_officers_email
      t.string    :responsible_officers_area
      t.string    :responsible_officers_address
      t.string    :companies_name
      t.string    :companies_tax_number
      t.string    :companies_staff_number
      t.string    :companies_area
      t.string    :companies_address
      t.string    :apply_code
      t.belongs_to  :scheme, index: true, foreign_key: true

      t.timestamps
    end
  end
end
