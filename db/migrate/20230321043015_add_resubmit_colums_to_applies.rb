class AddResubmitColumsToApplies < ActiveRecord::Migration[6.1]
  def change
    add_column :applies, :current_status, :string, default: 'registered', null: false
    add_column :applies, :is_need_resubmit_m1, :boolean, default: false
    add_column :applies, :is_need_resubmit_id_card, :boolean, default: false
    add_column :applies, :is_need_resubmit_photos, :boolean, default: false
    add_column :applies, :is_need_resubmit_other, :boolean, default: false
    add_column :applies, :resubmit_remark, :text
  end
end
