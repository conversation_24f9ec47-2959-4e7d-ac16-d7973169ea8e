# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t 'hello'
#
# In views, this is aliased to just `t`:
#
#     <%= t('hello') %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# The following keys must be escaped otherwise they will not be retrieved by
# the default I18n backend:
#
# true, false, on, off, yes, no
#
# Instead, surround them with single quotes.
#
# en:
#   'true': 'foo'
#
# To learn more, please read the Rails Internationalization guide
# available at https://guides.rubyonrails.org/i18n.html.

en:
  date:
    formats:
        default: "%Y/%m/%d"
        short: "%m/%d"
        long: "%Y年%m月%d日(%a)"
    month_names: [~, 1月, 2月, 3月, 4月, 5月, 6月, 7月, 8月, 9月, 10月, 11月, 12月]
    abbr_month_names: [~, 1月, 2月, 3月, 4月, 5月, 6月, 7月, 8月, 9月, 10月, 11月, 12月]

    order:
    - :year
    - :month
    - :day

  devise:
    sessions:
      user:
        signed_in: "Sign In ok"
        signed_out: "Sign Out ok"


  hello: "Hello world"
  next_step: "Next Step"
  return: "Return"
  query: "Query"
  agree: "I argee"
  not_argee: "I do not agree"
  project_name: "2023 Back-Office Digital Support Services for SMEs"
  project_name_2024: "2024 Digital Support Services for SMEs"
  project_name_2024_scheme2: "2024 Second Phase Digital Support Services for SMEs"
  project_name_2025: "2025 Digital Support Services for SMEs"
  application_form: "Application Form"
  new_apply: "New Apply"
  query_apply_status: "Query Apply Status"
  query_apply_and_resubmit: "Inquiry about application status And Resubmit Documents"
  declaration_on_collection_of_personal_information: "Declaration on collection of personal information"
  declaration_information_1: "The personal data provided for this application will only be used to perform the work within the scope of the 2024 Digital Support Services for SMEs."
  declaration_information_2: "For the purpose of processing the application, the information may be transferred to the authorized entities to do so."
  declaration_information_3: "Based on the fulfillment of legal obligations, the information may also be transferred to police authorities, judicial authorities and other authorized entities."
  declaration_information_4: "Applicants have the right to apply for access to, correction or update of personal information."
  enterprise_must_documents: "The enterprise must submit/upload the following documents:"
  enterprise_submit_document_1: "Support Service Application Form"
  enterprise_submit_document_2: "Copy of business tax in M/1 format [business start-up/change application form] A copy of both sides."
  enterprise_submit_document_3: "Upload a copy of the personal identification document of the business owner or legal representative of the business entity (the identification document must match the signature of the business owner shown on the M/1 document)."
  enterprise_submit_document_4: "Fill in the attendance information for participants of the digital operations awareness training course.."
  enterprise_submit_document_5: "Upload a photo of the shopfront that includes the sign or a photo of the office front that contains the company name (ensure that the sign of the operating location shown in the M/1 document is clearly visible for verification) and a photo showing the business in operation (ensure that the operational status of the business is clearly visible)."
  enterprise_submit_document_6: "Upload a copy of the business registration certificate, i.e., BR (if the business owner is a limited company)."
  enterprise_submit_document_7: "Upload the receipt for the application of the certified shop or the proof document indicating that you have become an certified shop."
  enterprise_submit_document_certified_shop: "Declaration of Application Status for 'certified shop' "
  please_input_numbers_to_next_steps: "Please enter the Taxpayer Number and Venue registration (Sales Tax File) to proceed to the next step"
  please_input_m1_to_next_steps: "Please enter the Venue registration (Sales Tax File) to proceed to the next step"
  application_form_cannot_change: "Once the form is submitted, it cannot be modified. Please check before submitting."
  required_fields: "*Required Fields"
  submit: "Submit"
  part1_information: "Part (1) - Basic Information of Business Owner"
  information_of_business_owner: "Information of Business Owner"
  company_name_in_m1: "Name(Company Name in M1):"
  taxpayer_number: "Taxpayer Number:"
  owner_and_taxpayer_number: "Taxpayer Number:"
  business_registration_number: "Business Registration Number (if any):"
  type: "type:"
  individual_business_owner: "Individual business owner"
  corporate_business_owner: "Corporate business owner"
  other_types: "Other types (please specify)"
  information_of_person_in_charge_of_the_enterprise: "Information of person in charge of the enterprise"
  information_of_contact_person: "Information of contact person"
  name: "Name:"
  mobile_phone_in_macao: "Mobile phone in Macao"
  for_sms: "(For SMS use)"
  position: "Position:"
  email: "Email:"
  corresponding_address: "Corresponding address:"
  area: "Area:"
  macao: "Macao"
  taipa: "Taipa"
  ilha_de_coloane: "Ilha de Coloane"
  address: "Address:"
  inquiry_phone_statement: "Inquiry phone number: 8898 0899"
  inquiry_hours_statement: "Inquiry hours: Monday to Friday (09:00 - 13:00, 14:30 - 17:45)"
  part2_information: "Part (2) - Information where Back-Office digital support system is to be installed"
  enterprise_information: "Enterprise information"
  venue_registration_number: "Venue registration (Sales Tax File) number:"
  apply_venue_registration_number: "Venue registration (Sales Tax File) number:"
  enterprise_name: "Enterprise name:"
  total_number_of_employees: "Total number of employees:"
  enterprise_industry: "Enterprise industry"
  enterprise_address: "Address:"
  agree_statement: "I/We hereby declare that all the information provided above is true and correct and I/we accept and agree to abide by the requirements set out in the Regulations and have not applied for financial assistance from other organizations for the above project."
  cpttm_contact_footer: "For any queries, please contact Information System and Technology Department, CPTTM. Tel: 8898 0899, email: <EMAIL>, or visit us at Rua de Xangai 175, Ed. ACM., 6 andar Macau."
  company_name_in_m1_required: "Name(Company Name in M1) Required"
  taxpayer_number_required: "Taxpayer Number Required"
  person_in_charge_of_the_enterprise_name_required: "Person in charge of the enterprise name Required"
  person_in_charge_of_the_enterprise_phone_required: "Person in charge of the enterprise phone Required"
  person_in_charge_of_the_enterprise_area_required: "Person in charge of the enterprise area Required"
  person_in_charge_of_the_enterprise_address_required: "Person in charge of the enterprise address Required"
  contact_person_of_the_enterprise_name_required: "Contact person name of the enterprise Required"
  contact_person_tel_of_the_enterprise_phone_required: "Contact person phone number of the enterprise Required"
  venue_registration_number_required: "Venue registration (Sales Tax File) number Required"
  enterprise_name_required: "Enterprise name Required"
  total_number_of_employees_required: "Total number of employees Required"
  enterprise_area_required: "Enterprise Area Required"
  enterprise_address_required: "Enterprise Address Required"
  industry_required: "Enterprise Industry Required"
  taxpayer_number_exist_already: "Taxpayer Number Exist Already"
  person_in_charge_phone_number_format_incorrect: "Person in charge of the enterprise phone number format incorrect"
  verify_your_m1_and_phone: "Input your venue registration number"
  verify_your_taxpayer_number: "Input your taxpayer number used during the application"
  in_charge_person_phone_number_online: "Person in charge of the enterprise phone number:"
  in_charge_person_phone_number: "Person in charge of the enterprise phone number:"
  contact_person_phone_number: "Contact person of the enterprise phone number:"
  no_matching_record: "No Matching Record"
  no_matching_taxpayer_number_record: "No Matching Taxpayer Number Record"
  apply_code: "Apply Code:"
  submitted_data: "Submitted data"
  input_verification_code: "Please input verification code:"
  incorrent_verfication_code: "Incorrect verfication code"
  input_verification_code_to_next_step: "Input verfication code to next step:"
  part3_information: "Part (3) - Confirmation of Submission of Appendices"
  m1_required: "M/1 Frontside Required"
  m1_backside_required: "M/1 Backside Required"
  id_card_copy_required: "ID card copy Required"
  store_pictures_required: "Store Pictures Required"
  certified_shop_required: "Certified Shop proof Required"
  m1_file_size_exceeds_limit: "M1 Frontside File size exceeds limit"
  m1_backside_file_size_exceeds_limit: "M1 Backside File size exceeds limit"
  id_card_copy_file_size_exceeds_limit: "ID card copy File size exceeds limit"
  store_pictures_file_size_exceeds_limit: "Store Pictures File Size exceeds limit"
  certified_shop_file_size_exceeds_limit: "Certified Shop proof File Size exceeds limit"
  others_document_file_size_exceeds_limit: "Others Document File Size exceeds limit"
  exceeding_file_upload_limit: "Exceeding file upload limit"
  application_period: "Application: From 2024-05-06 to 2024-05-26 23:59."
  application_period_closed: "Applications are now closed. The application period was from July 4, 2023 to August 4, 2023 at 23:59."
  regulations_and_detail: "Regulations And Details"
  excessive_sms: "Excessive SMS sending frequenc"
  failed_send_sms: "Failed to send notification SMS"
  valid_code: "Valid Code:"
  input_valid_code_to_next_step: "Please input valid code to next step"
  valid_code_send_to_contact_person_tel: "Valid code send to shop contact person phone number, expires in 5 minutes"
  valid_code_incorrect: "Valid Code Incorrect"
  valid_code_try_times_over: "Exceeded the maximum number of valid code attempts"
  company_tax_number_cannot_be_changed: "company tax number cannot be changed after registration"
  copy_of_business_tax_in_m1_format: "Copy of business tax in M/1 format [business start-up/change application form]"
  front_side: "front side"
  back_side: "back side"
  identity_document_of_the_individual_business_owner_or_legal: "Upload a copy of the identity document of the individual business owner or legal
representative.(front side and reverse side)"
  two_photos: "Two photos of the store:"
  two_photos_first: "1) frontage with signage(The signboard name must be the same as the business name registered at the M1 location)"
  two_photos_second: "2) business situation"
  certified_shop_documents: "Receipt for applying for the Certified Store franchise or proof of having become an Certified Store (if there are no relevant documents, you can check the application status of the Certified Store at the following link and provide a screenshot as proof)."
  inquiry_about_certified_shop: "Inquiry about Certified shop proof (application receipt/certificate)"
  others_business_registration_documents: "Others, such as Business Registration documents for Corporate business owner (Valid for 3 months)"
  sample: "Sample"
  submitted_missing_file: "submitted missing files"
  the_submitted_data_has_been_verified_as_complete: "The submitted data has been verified as complete"
  submitting_missing_files_completed: "submitting missing files completed"
  registered_catering_statement: "For the enterprises that have participated in and been funded by the 2021 and 2022 Back-Office Electronic Funding Schemes in the Catering Industry, If successfully in the drawing lots, this will be an upgraded scheme. The value limit is MOP 6,000.00 (service to be provided by the original system supplier)."
  registered_before_statement: "If you have previously applied for and received funding from the 2021 and 2022 and 2023 Back-Office Electronic Funding Schemes in the Catering Industry or the '2023 Back-Office Digital Support Services for SMEs', you are not eligible to apply"
  registered_finished_statement: "Your application and related attachments have been successfully submitted. If there are any omissions or errors in the documents after review, you will be notified by a specialist."
  drawing_lots_statement: "The drawing lots results will be announced on our center's website on the date of May 30th."
  participated_catering_statement: "Your company has participated in the 2021 and 2022 Back-Office Electronic Funding Schemes in the Catering Industry and has been awarded funding. If you are successfully in the drawing lots, this will be an upgraded scheme. The value limit is MOP 6,000.00 (service to be provided by the original system supplier)."
  course_agreement_statement: "I/We agree to send personnel (up to two) to attend the Digital Business Operations Awareness Course at your center at the designated time"
  course_attendee_information: "Information of personnel (up to two people) attending the Digital Business Operations Awareness Course"
  course_start_sequential: "The digital operation awareness training course will be sequentially deployed starting from August 21, 2023, according to the drawing lots results"
  attendee_name_1: "The name of the first attendee: (Same as the name on the ID Card)"
  attendee_title_1: "The title of the first attendee: "
  attendee_name_2: "The name of the second attendee: (Same as the name on the ID Card)"
  attendee_title_2: "The title of the second attendee: "
  attendee_name_1_required: "The Name Of The First Attendee Required"
  attendee_title_1_required: "The Title Of The First Attendee Required"
  draw_result_order: "Draw Result: "
  company_tax_number_register_before: "Copy of company number %{companies_tax_number} had previously applied for and received funding from the 2021 and 2022 and 2023 Back-Office Electronic Funding Schemes in the Catering Industry or the '2023 and 2024 Back-Office Digital Support Services for SMEs', and not eligible to apply"
  company_tax_number_register_duplicate: "Company number %{companies_tax_number} has already applied for this program. The same company cannot apply again."
  this_owner_tax_number_duplicate: "This taxpayer with identification number has registered an application for this program before and cannot not register again. You can use this taxpayer identification number to inquire about the application status."
  this_company_number_duplicate: "This M1 number has already applied for this program. The same company cannot apply again."
  owner_tax_number_register_duplicate: "The taxpayer with identification number %{owner_tax_number}  has registered an application for this program before and cannot not register again. You can use this taxpayer identification number to inquire about the application status."
  owner_tax_number_register_times_over: "The registration count for the same taxpayer identification number %{owner_tax_number} has exceeded 3 times."
  certified_shop_application_status: "Certified shop application status"
  certified_shop_enterprise_applied: "Applied"
  certified_shop_in_progress: "Application in progress"
  certified_shop_not_applied: "Not applied"
  can_apply_only_three_companies: "Each taxpayer number can only apply for a maximum of three companies"
  the_number_applying_company: "Applying Company %{number}"
  submission_completed: "Submission Completed"
  new_company_application: "New Comapny Application"
  required_to_resubmit: "This applying company is required to submit additional documents."
  select_training_course_date: "Select a training course date"
  available_class_time_slot: "Available class time slot %{number}"
  late_submission: "Late Submission"
  course_arrangement_base_on_drawing_statement: "The course will be held from July 16th to August 30th, for a total of 6 hours. Please select the available dates for the class. The final class date will be one of the selected dates below. And will randomly draw from the selected dates and notify class date accordingly."
  query_and_reapply: "Query and Reapply Using the Same Taxpayer Number"
  more_than_three_based_on_order: "The selection will be conducted through a drawing system to draw and rank all application cases, and will review the applications in the order determined by the drawing"
  additional_documents_period: "The deadline for the submission of additional documents from 300 companies in the first phase is from now until September 11, 2024, at 23:59."
  second_phase_end_statement: "The second phase of applications has been closed for submissions as of November 29."
  2025_start_statement: "Registration for the first phase will be accepted from June 3, 2025, to June 22, 2025."
  already_have_prefer_supplies: "Already have a preferred supplier? (Sorted by stroke count)"
  none: "No"
  online_application_date: "Online Application Date"
  first_phase_apply_statement: "First phase online application: June 3, 2025, to June 22, 2025"
  second_phase_apply_statement: "Second phase online application: November 3, 2025, to November 21, 2025."
  upload_factory_license_statement: "If the business location is in an industrial building, it is necessary to upload the 'Licença Industrial'"
  confirm_the_submitted: "Please confirm the submitted information."
  confirm_submission: "Confirm Submission"
  cancel: "Cancel"
  information_submission: "Supplementary information submission"
  last_submission_date: "Last Submission Late"

    #後台使用
  finished_training: "完成培訓"
  to_assessment: "前往診斷"
  submit_assessment_report: "發出診斷報告"
  submit_quotation: "供應商提交報價"
  submit_agreement_report: "發出同意安裝通知書"
  submit_settlement: "商戶提交結算資料"
  to_audit: "前往稽查"
  submit_epq: "發出EPQ"
  case_finished: "個案完成"