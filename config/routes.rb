Rails.application.routes.draw do

  post 'applies/query', to: 'applies#query'
  resources :applies, except: [:show] do

    member do
      get :late  #for 後台補交資料
      get :more
       #patch :pass
       #patch :no_pass
      get :detail
      patch :late, to: 'applies#late_update'
       #post :status_to_need_resubmit
       #post :audit_status_to_approved
       #post :audit_status_to_question
       #get :audit
      get :resubmit
      patch :resubmit_update
      patch :hide_attachment
      get :upload
      #patch :give_up
    end

    collection do
      get :start
      post :start
      get :query
      get :search
      get :all
      get :all_detailed
      get :finished
      get :info
      get :rights
       #get :renew #同一納稅人編號多次申請
       #post :renew
       #patch :renew, to: 'applies#recreate'
       #get :audit_index   #for management confirm before drawing
    end

    resources :confirmations do
      post :status_to_confirmed
    end

    resources :assessments, only: [:edit, :update] do
      member do
        get :report
        get :generate_report
        get :upload_checking
        patch :update_upload_checking
        patch :report_update
        post :create_suggestion
      end
      collection do
        get :all
        get :upload_only_list
      end
    end

    resources :timelines
    resources :comments, only: [:create]
  end

  resources :schemes
  resources :supplies_applies do
    member do
      get :cpttm_show
      get :cpttm_edit
      patch :cpttm_update
      get :match_index
      post :match
      get :install_agreement
      patch :install_agreement_update
      get :settlement
      patch :settlement_update
      get :install_report
      patch :install_report_update
      get :generate_install_report
    end
    collection do
      get :cpttm_index
      get :recommended_applies
      get :submitted_settlement_list
    end
  end

  resources :drawings, only: [:show] do
    collection do
      get :seed
      get :confirm_seed
      get :all
      post :start_draw
      get :list_draw
      get :list_detail
      post :confirm_draw
    end
  end

  resources :approvals, only: [:index] do
    member do
      get :process_form
      post :handle
      patch :handle
    end
  end

  resources :settlement_approvals, only: [:index] do
    member do
      get :process_form
      post :handle
      patch :handle
    end
  end

  resources :reminds, only: [:index, :edit, :update]

  devise_for :users

  get '/dashboard', to: "dashboard#index"
  get '/dashboard/management', to: "dashboard#management"
  get '/user', to: "applies#all", :as => :user_root


  root 'applies#index'
end
