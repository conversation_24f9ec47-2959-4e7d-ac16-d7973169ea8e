GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.0.9)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    active_storage_validations (1.0.3)
      activejob (>= 5.2.0)
      activemodel (>= 5.2.0)
      activestorage (>= 5.2.0)
      activesupport (>= 5.2.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
      zeitwerk (~> 2.3)
    addressable (2.8.4)
      public_suffix (>= 2.0.2, < 6.0)
    airbrussh (1.4.1)
      sshkit (>= 1.6.1, != 1.7.0)
    babel-source (5.8.35)
    babel-transpiler (0.7.0)
      babel-source (>= 4.0, < 6)
      execjs (~> 2.0)
    barby (0.6.9)
    bcrypt (3.1.18)
    bindex (0.8.1)
    bootsnap (1.16.0)
      msgpack (~> 1.2)
    builder (3.2.4)
    byebug (11.1.3)
    capistrano (3.17.2)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (2.1.0)
      capistrano (~> 3.1)
    capistrano-rails (1.6.2)
      capistrano (~> 3.1)
      capistrano-bundler (>= 1.1, < 3)
    capistrano-rails-collection (0.1.0)
      capistrano-rails (~> 1.1)
    capistrano-rake (0.2.0)
      capistrano (>= 3.0)
    capistrano-rbenv (2.2.0)
      capistrano (~> 3.1)
      sshkit (~> 1.3)
    capistrano3-puma (5.2.0)
      capistrano (~> 3.7)
      capistrano-bundler
      puma (>= 4.0, < 6.0)
    capybara (3.39.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.8)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    concurrent-ruby (1.2.2)
    crass (1.0.6)
    date (3.3.3)
    devise (4.9.2)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    erubi (1.12.0)
    execjs (2.8.1)
    ffi (1.15.5)
    foundation-rails (*******)
      railties (>= 3.1.0)
      sass (>= 3.3.0, < 3.5)
      sprockets-es6 (>= 0.9.0)
    globalid (1.1.0)
      activesupport (>= 5.0)
    http-accept (1.7.0)
    http-cookie (1.0.5)
      domain_name (~> 0.5)
    i18n (1.13.0)
      concurrent-ruby (~> 1.0)
    image_processing (1.12.2)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    listen (3.8.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    loofah (2.20.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.2)
    matrix (0.4.2)
    method_source (1.0.0)
    mime-types (3.4.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2023.0218.1)
    mini_magick (4.12.0)
    mini_mime (1.1.2)
    mini_portile2 (2.8.2)
    minitest (5.18.0)
    msgpack (1.7.0)
    mysql2 (0.5.5)
    net-imap (0.3.4)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.1)
      timeout
    net-scp (4.0.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-smtp (0.3.3)
      net-protocol
    net-ssh (7.1.0)
    netrc (0.11.0)
    nio4r (2.5.9)
    nokogiri (1.14.3)
      mini_portile2 (~> 2.8.0)
      racc (~> 1.4)
    orm_adapter (0.5.0)
    pagy (6.0.3)
    public_suffix (5.0.1)
    puma (5.6.5)
      nio4r (~> 2.0)
    racc (1.6.2)
    rack (2.2.7)
    rack-mini-profiler (2.3.4)
      rack (>= 1.2.0)
    rack-proxy (0.7.6)
      rack
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
      sprockets-rails (>= 2.0.0)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.5.0)
      loofah (~> 2.19, >= 2.19.1)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
    rake (13.0.6)
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    regexp_parser (2.8.0)
    responders (3.1.0)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.2.5)
    ruby-vips (2.1.4)
      ffi (~> 1.12)
    rubyzip (2.3.2)
    rucaptcha (2.6.1)
      railties (>= 3.2)
    sass (3.4.25)
    sass-rails (5.1.0)
      railties (>= 5.2.0)
      sass (~> 3.1)
      sprockets (>= 2.8, < 4.0)
      sprockets-rails (>= 2.0, < 4.0)
      tilt (>= 1.1, < 3)
    selenium-webdriver (4.9.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    semantic_range (3.0.0)
    simple_form (5.2.0)
      actionpack (>= 5.2)
      activemodel (>= 5.2)
    spring (4.1.1)
    sprockets (3.7.2)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-es6 (0.9.2)
      babel-source (>= 5.8.11)
      babel-transpiler
      sprockets (>= 3.0.0)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    sshkit (1.21.4)
      net-scp (>= 1.1.2)
      net-ssh (>= 2.8.0)
    thor (1.2.1)
    tilt (2.1.0)
    timeout (0.3.2)
    trix (0.10.1)
      rails
    turbolinks (5.2.1)
      turbolinks-source (~> 5.2)
    turbolinks-source (5.2.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.0)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webdrivers (5.2.0)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0)
    webpacker (5.4.4)
      activesupport (>= 5.2)
      rack-proxy (>= 0.6.1)
      railties (>= 5.2)
      semantic_range (>= 2.3.0)
    websocket (1.2.9)
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.8)

PLATFORMS
  ruby

DEPENDENCIES
  active_storage_validations
  barby
  bootsnap (>= 1.4.4)
  byebug
  capistrano (~> 3.10)
  capistrano-rails (~> 1.3)
  capistrano-rails-collection
  capistrano-rake
  capistrano-rbenv (~> 2.1)
  capistrano3-puma (= 5.2.0)
  capybara (>= 3.26)
  devise
  foundation-rails (~> 6.3.1)
  image_processing
  jbuilder (~> 2.7)
  listen (~> 3.3)
  mini_magick (~> 4.8)
  mysql2 (>= 0.4.4, < 0.6.0)
  pagy
  puma (~> 5.0)
  rack-mini-profiler (~> 2.0)
  rails (~> 6.1.7, >= *******)
  rest-client
  rucaptcha (= 2.6.1)
  sass-rails (~> 5.0)
  selenium-webdriver (>= 4.0.0.rc1)
  simple_form
  spring
  trix
  turbolinks (~> 5)
  tzinfo-data
  web-console (>= 4.1.0)
  webdrivers
  webpacker (~> 5.0)

RUBY VERSION
   ruby 2.7.2p137

BUNDLED WITH
   2.1.4
