task :update_certified_status => :environment do

  desc "Update certified status"

  companies_tax_number = ["312542", "283991", "244459", "317081", "211436", "298779", "304471", "133957", "111218", "245532", "186056", "177747", "198280", "251610", "322733", "301567", "307614", "084313", "168944", "182100", "314699", "294472", "315257", "316215", "304723", "316109", "324338", "084915", "254990", "262909", "264874", "318553", "185961", "178733", "311781", "026116", "115572", "085712", "282266", "292301", "186306", "314368", "165487", "305499", "312002", "259466", "317496", "313571", "322269", "033085", "105163", "278744", "242252", "206898", "314533", "128642", "318487", "314244", "266617", "211657", "213192", "180172", "297560", "106209", "301233", "321508", "281667", "298650", "235094", "258804", "317784", "196775", "281163", "136489", "223035", "161938", "294753", "268189", "251330", "052193", "216269", "286991", "311789", "286968", "255997", "270026", "105406", "310151", "312160", "275532", "285106", "211290", "266375", "288117", "301198", "181163", "233058", "260473", "134393", "022676", "226912", "317601", "312356", "313700", "185352", "293967", "189094", "221665", "312328", "268629", "130203", "290597", "311994", "237496", "032134", "306827", "322613", "313195", "311638", "323627", "218012", "110185", "262566", "295130", "206830", "187973", "206153", "237086", "211655", "212904", "315017", "171486", "310244", "312433", "245377", "283908", "298532", "295267", "291846", "199330", "096063", "189812", "266817", "225569", "263291", "250370", "251174", "292802", "217884", "314559", "280001", "249967", "234839", "270709", "234188", "171531", "285783", "318196", "314382", "303588", "321986", "278254", "232097", "310562", "271535", "253407", "311168", "192267", "300283", "291185", "308514", "261166", "312089", "310586", "268609", "238425", "215997", "188724", "127142", "309721", "311537", "298906", "314349", "320302", "168162", "191743", "249785", "320478", "271957", "310487", "282315", "208523", "294316", "212259", "218140", "234617", "221894", "320756", "311877", "323157", "236003", "210609", "149136", "187002", "291897", "320691", "168313", "284202", "293673", "220483", "208963", "213060", "310394", "236531", "322484", "320600", "205732", "247285", "281379", "273013", "309228", "263546", "212196", "196947", "270086", "309153", "259064", "281248", "207244", "307463", "293224", "138274", "296198", "155822", "181028", "063844", "315818", "276355", "202668", "311988", "323129", "252029", "072003", "250047", "315724", "319506", "268120", "175978", "187854", "179182", "167563", "106916", "287673", "298295", "318735", "300438", "311021", "324019", "286326", "136477", "160936", "286365", "260423", "237972", "274668", "319188", "293651", "292160", "226398", "263006", "313312", "197953", "194247", "172529", "310721", "318889", "323432", "211563", "297966", "272521", "292742", "311051", "306214", "310364", "317271", "192019", "176288", "310714", "301899", "311628", "268108", "213371", "240511", "305289", "313422", "283907", "211060", "304208", "205788", "287986", "320848", "223231", "290389", "307751", "283271", "206342", "236645", "292679", "321739", "268196", "275967", "306418", "275970", "146358", "272806", "075027", "279094", "314499", "281396", "290661", "314358", "321897", "313973", "223956", "238102", "212389", "223175", "173383", "276531", "229277", "321890", "145579", "280444", "240412", "286617", "268248", "235462", "312516", "311844", "221428", "287450", "240387", "310139", "302743", "310850", "156724", "298875", "305598", "225663", "312449", "317658", "312368", "243797", "244609", "190001", "312323", "314405", "204832", "314374", "069804", "252871", "247678", "236341", "255533", "298636", "280873", "166414", "308528", "288852", "281557", "079875", "315459", "229622", "171415", "282660", "282049", "284421", "321868", "295400", "213524", "284395", "233437", "274466", "242580", "122719", "320706", "307032", "162659", "176953", "101121", "118016", "306392", "024204", "320651", "315399", "245390", "322263", "283937", "291786", "303142", "287962", "218838", "308157", "200279", "264885", "215540", "316614", "312077", "092322", "299457", "281619", "075703", "169655", "229255", "121738", "321725", "302715", "176709", "322889", "305076", "221545", "095514", "212467", "301128", "201489", "074781", "289312", "289310", "289233", "316013", "313246", "078667", "281912", "288439", "284575", "249151", "306892", "272725", "324153", "255705", "321859", "321858", "316906", "312381", "251621", "322429", "303130", "294557", "292582", "298707", "221542", "301096", "309656", "273451", "304718", "295690", "301931", "048112", "264237", "295111", "288216", "270227", "250035", "319286", "247292", "247296", "075044", "160705", "208650", "151388", "295211", "276082", "252924", "302579", "313461", "313717", "213010", "319222", "250442", "136340", "244295", "272240", "087964", "204840", "305967", "317303", "157106", "199691", "164606", "204348", "219411", "270135", "045093", "291271", "294144", "293550", "315918", "323184", "300041", "314700", "056940", "268235", "305466", "032184", "301005", "303897", "308789", "169900", "251114", "262639", "204756", "203665", "299441", "311055", "269551", "289386", "278040", "295579", "160323", "176392", "043548", "293197", "290403", "110384", "127029", "152754", "315715", "167310", "319364", "311473", "299640", "257812", "285482", "318609", "275419", "230643", "282997", "314501", "307813", "225538", "314241", "307318", "283073", "290564", "222880", "291234", "317719", "319814", "276028", "153990", "312059", "295221", "235478", "212178", "222900", "309932", "301032", "214504", "314876", "317826", "311138", "191305", "317534", "322409", "311748", "314965", "193467", "195480", "320610", "170205", "135240", "226764", "307115", "312775", "285786", "303481", "312006", "311655", "269044", "316563", "312482", "214586", "189922", "138907", "260383", "198792", "277734", "255789", "258560", "292482", "115229", "312939", "225219", "310646", "251675", "291545", "305431", "106679", "102731", "203359", "243660", "167193", "179430", "182833", "268477", "040298", "142930", "221828", "201657", "178918", "276363", "036896", "169192", "319561", "066677", "272374", "145692", "301616", "263672", "205953", "279922", "067464", "170158", "139489", "195336", "316658", "196363", "117053", "300800", "297421", "286765", "214781", "304960", "247991", "301887", "214910", "200568", "101139", "321654", "294019", "185524", "260891", "291089", "314114", "300008", "212990", "253279", "311745", "313567", "305526", "182055", "236388", "142897", "225467", "312291", "314562", "174166", "240505", "201987", "150719", "233431", "233436", "249669", "282859", "307824", "199404", "322430", "138262", "013645", "291281", "254299", "184174", "322318", "265154", "315664", "308295", "287817", "314208", "047624", "305678", "112299", "297082", "071121", "321311", "282162", "111445", "228961", "113025", "252210", "315004", "049239", "313222", "305794", "283005", "301587", "313355", "312706", "263703", "192309", "217007", "262785", "249928", "248748", "205302", "180805", "314843", "088180", "295664", "295698", "233135", "237457", "292595", "314316", "311388", "291994", "295471", "299578", "287420", "306262", "292490", "314252", "114252", "264198", "293473", "145200", "245846", "323901", "196312", "199698", "269745", "310685", "311925", "298506", "217402", "322459", "311306", "163120", "010075", "134320", "087388", "186072", "111117", "322267", "280960", "199421", "310267", "296667", "204054", "251512", "280845", "064000", "267961", "046490", "104171", "094803", "303718", "318379", "318382", "294135", "084096", "312443", "288120", "315170", "281090", "203558", "265914", "155771", "293362", "318694", "218163", "267257", "291752", "250331", "280443", "204250", "310743", "311289", "143481", "121271", "078668", "320057", "284331", "224210", "314659", "305216", "315908", "324076", "296332", "207123", "316640", "148149", "278606", "278475", "278482", "275917", "179428", "165699", "291968", "323161", "320630", "307846", "307837", "225030", "268355", "088511", "299323", "303117", "261991", "323941", "266593", "264472", "230770", "066056", "166923", "277627", "262838", "317531", "250914", "307902", "310940", "317159", "257965", "072992", "229654", "200400", "262278", "263116", "217062", "191461", "245247", "208332", "171528", "186716", "230285", "208331", "239171", "183861", "198166", "185938", "195313", "208863", "251287", "237265", "245881", "271515", "279628", "289961", "160319", "261694", "278868", "287637", "287636", "312836", "261362", "287705", "168779", "220019", "181820", "312298", "290350", "291134", "294585", "236342", "252870", "317158", "244558", "227167", "306165", "301914", "246093", "235068", "203515", "098295", "188848", "180632", "321860", "288211", "236073", "217920", "264093", "182472", "201966", "226134", "245464", "263946", "263216", "323062", "279532", "173700", "295008", "294978", "235064", "249930", "246282", "253383", "251554", "195433" ]

  companies_tax_number.each do |number|

    @apply = Apply.by_scheme(2).find_by(companies_tax_number: number)

    if @apply.nil?
      puts "Update not OK, companies_tax_number #{number} not found !!!!!"
      next
    end

    @apply.update_attribute(:is_confirmed_certified_shop, true)

    puts "companies_tax_number #{number} Updated"
  end
end