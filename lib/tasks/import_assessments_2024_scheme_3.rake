task :import_assessments_2024_scheme_3 => :environment do

  desc "Import assessment data by json file"

  file_path = File.expand_path('assessments_2024_scheme_3.json', __dir__)
  json_data = File.read(file_path)
  assessments = JSON.parse(json_data)

  assessments.each do |assessment|
    @apply = Apply.by_scheme(3).find_by(apply_code: assessment['apply_code'], companies_tax_number: assessment['companies_tax_number'])
    if @apply.nil? || @apply.assessment.nil?
      puts "Update not OK, No apply_code #{assessment['apply_code']} assessment found !!!!!"
      next
    end

    if !@apply.in_lots?
      puts "Update not OK, apply_code #{assessment['apply_code']} not in drawing lots !!!!!"
      next
    end

    @assessment = @apply.assessment
    @assessment.shop_age = assessment['shop_age']
    @assessment.shop_type = assessment['shop_type']
    @assessment.client_target = assessment['client_target']
    @assessment.it_level = assessment['it_level']
    @assessment.social_media_count = assessment['social_media_count']
    @assessment.simple_easy_pay = assessment['simple_easy_pay']
    @assessment.online_payment_count = assessment['online_payment_count']
    @assessment.business_diff = assessment['business_diff']
    @assessment.business_expectations = assessment['business_expectations']
    @assessment.it_support = assessment['it_support']

    @assessment.is_open_data_to_supplies = true
    @assessment.save

    puts "apply_code #{assessment['apply_code']} assessment Updated"
  end
end