task :import_init_suggestions => :environment do

  desc "Import initial suggestions"

  suggestions = [
  "安裝電子化POS系統",
  "強化線上營銷",
  "善用主流外賣平台",
  "增加在不同平台上的宣傳策略",
  "建立私域流量商城",
  "增加線上商城，建立私域流量",
  "安裝及使用電子化POS倉存管理系統",
  "增加不同的電子支付方式",
  "增加不同的國際電子支付方式",
  "善用或升級現時的POS系統",
  "使用聚合支付系統",
  "提高企業品牌認知度",
  "推廣宣傳至新式社交平台",
  "針對不同年齡層客人優化銷售方案"]

  suggestions.each do |suggestion|
    Suggestion.create!(
      content: suggestion,
    )
  end

  puts "Successfully imported #{suggestions.count} suggestions!"

end