task :import_assessments => :environment do

  desc "Import assessment data by json file"

  file_path = File.expand_path('assessments.json', __dir__)
  json_data = File.read(file_path)
  assessments = JSON.parse(json_data)

  assessments.each do |assessment|
    @apply = Apply.find_by(apply_code: assessment['apply_code'])
    if @apply.nil? || @apply.assessment.nil?
      puts "Update not OK, No apply_code #{assessment['apply_code']} assessment found !!!!!"
      next
    end

    if !@apply.in_lots?
      puts "Update not OK, apply_code #{assessment['apply_code']} not in drawing lots !!!!!"
      next
    end

    @assessment = @apply.assessment
    @assessment.shop_age = assessment['shop_age']
    @assessment.shop_type = assessment['shop_type']
    @assessment.employee_avg_age = assessment['employee_avg_age']
    @assessment.client_target = assessment['client_target']
    @assessment.it_level = assessment['it_level']
    @assessment.social_media_count = assessment['social_media_count']
    @assessment.simple_easy_pay = assessment['simple_easy_pay']
    @assessment.online_payment_count = assessment['online_payment_count']
    @assessment.external_environment_difficult = assessment['external_environment_difficult']
    @assessment.operating_difficult = assessment['operating_difficult']
    @assessment.company_strategy = assessment['company_strategy']
    @assessment.full_time_employee = assessment['full_time_employee']
    @assessment.part_time_employee = assessment['part_time_employee']
    @assessment.business_expectations = assessment['business_expectations']

    @assessment.is_open_data_to_supplies = true
    @assessment.save

    puts "apply_code #{assessment['apply_code']} assessment Updated"
  end
end